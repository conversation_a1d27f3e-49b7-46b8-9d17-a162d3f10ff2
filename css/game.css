/* 圍棋遊戲頁面樣式 */

/* 全域樣式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', 'Microsoft JhengHei', sans-serif;
    background: linear-gradient(135deg, #8B4513 0%, #D2691E 50%, #F4A460 100%);
    min-height: 100vh;
    color: #2c1810;
    overflow-x: hidden;
}

/* 遊戲容器 */
.game-container {
    background: rgba(255, 255, 255, 0.95);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 標題列 */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 30px;
    background: linear-gradient(135deg, #2c1810, #4a2c1a);
    color: #f5f5dc;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.header-left,
.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-center h1 {
    font-size: 1.8rem;
    font-weight: bold;
}

.game-info {
    display: flex;
    gap: 15px;
    font-size: 0.9rem;
    opacity: 0.8;
}

.header-btn {
    padding: 8px 16px;
    background: rgba(245, 245, 220, 0.2);
    color: #f5f5dc;
    border: 1px solid rgba(245, 245, 220, 0.3);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.header-btn:hover {
    background: rgba(245, 245, 220, 0.3);
    transform: translateY(-1px);
}

/* 主要遊戲區域 */
.game-main {
    flex: 1;
    display: flex;
    padding: 20px;
    gap: 20px;
    align-items: flex-start;
}

/* 玩家面板 */
.player-panel {
    width: 250px;
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.player-info {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.player-avatar {
    font-size: 3rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #f8f9fa;
    border: 3px solid #dee2e6;
}

.player-avatar.black {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
}

.player-avatar.white {
    background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
    color: #2c3e50;
}

.player-details h3 {
    font-size: 1.2rem;
    margin-bottom: 8px;
    color: #2c1810;
}

.player-stats {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
}

.stat-label {
    color: #666;
}

.stat-value {
    font-weight: bold;
    color: #2c1810;
}

/* 遊戲控制按鈕 */
.game-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.control-btn {
    padding: 12px;
    border: 2px solid #8B4513;
    border-radius: 8px;
    background: white;
    color: #2c1810;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.control-btn:hover {
    background: #f5f5dc;
    transform: translateY(-2px);
}

.control-btn.resign {
    border-color: #dc3545;
    color: #dc3545;
}

.control-btn.resign:hover {
    background: #dc3545;
    color: white;
}

/* 棋盤容器 */
.board-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 600px;
    margin: 0 auto;
}

.board-wrapper {
    position: relative;
    background: #DEB887;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    margin-bottom: 20px;
}

.go-board {
    display: block;
    cursor: crosshair;
    border-radius: 5px;
}

.board-overlay {
    position: absolute;
    top: 20px;
    left: 20px;
    pointer-events: none;
}

/* 棋盤控制按鈕 */
.board-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.board-control-btn {
    padding: 10px 20px;
    background: #4a90e2;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.board-control-btn:hover:not(:disabled) {
    background: #357abd;
    transform: translateY(-2px);
}

.board-control-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* 遊戲狀態 */
.game-status {
    text-align: center;
    padding: 15px;
    background: rgba(245, 245, 220, 0.8);
    border-radius: 10px;
    border: 2px solid #8B4513;
    font-size: 1.1rem;
    font-weight: bold;
    color: #2c1810;
}

/* AI 思考指示器 */
.ai-thinking {
    text-align: center;
    padding: 20px;
    background: rgba(74, 144, 226, 0.1);
    border-radius: 10px;
    border: 2px solid #4a90e2;
    margin-bottom: 20px;
}

.thinking-animation {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-bottom: 10px;
}

.thinking-dot {
    width: 8px;
    height: 8px;
    background: #4a90e2;
    border-radius: 50%;
    animation: thinking 1.4s infinite ease-in-out;
}

.thinking-dot:nth-child(1) { animation-delay: -0.32s; }
.thinking-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes thinking {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 棋譜記錄 */
.move-history {
    margin-top: 20px;
}

.move-history h4 {
    margin-bottom: 10px;
    color: #2c1810;
    font-size: 1rem;
}

.move-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    background: #f8f9fa;
    font-size: 0.85rem;
}

/* 分數顯示 */
.game-score h4 {
    margin-bottom: 15px;
    color: #2c1810;
    text-align: center;
}

.score-display {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.score-item {
    display: flex;
    justify-content: space-between;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 5px;
    font-size: 0.9rem;
}

.score-label {
    color: #666;
}

.score-value {
    font-weight: bold;
    color: #2c1810;
}

/* AI vs AI 控制面板 */
.ai-vs-ai-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    border-radius: 10px;
    margin-bottom: 15px;
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.speed-control {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
}

.speed-control label {
    font-size: 0.9rem;
    white-space: nowrap;
}

.speed-control select {
    padding: 5px 8px;
    border: none;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-weight: bold;
    cursor: pointer;
}

.ai-vs-ai-controls .board-control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-size: 0.9rem;
    padding: 8px 16px;
}

.ai-vs-ai-controls .board-control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

/* 隱藏類別 */
.hidden {
    display: none !important;
}

/* 模態對話框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 15px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-content h2 {
    text-align: center;
    margin-bottom: 20px;
    color: #2c1810;
    font-size: 1.5rem;
}

.modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
}

.modal-btn {
    padding: 12px 25px;
    border: 2px solid #8B4513;
    border-radius: 8px;
    background: white;
    color: #2c1810;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.modal-btn.primary {
    background: #4a90e2;
    border-color: #4a90e2;
    color: white;
}

.modal-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 最終計分 */
.final-score {
    margin: 20px 0;
}

.score-breakdown h3 {
    text-align: center;
    margin-bottom: 15px;
    color: #2c1810;
}

.score-details {
    display: flex;
    justify-content: space-between;
    gap: 20px;
}

.player-final-score {
    flex: 1;
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.player-name {
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 10px;
    display: block;
}

.score-items {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-size: 0.9rem;
}

/* 設定對話框 */
.settings-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.setting-item label {
    font-weight: bold;
    color: #2c1810;
}

.setting-item input,
.setting-item select {
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* 響應式設計 */
@media (max-width: 1024px) {
    .game-main {
        flex-direction: column;
        align-items: center;
    }
    
    .player-panel {
        width: 100%;
        max-width: 600px;
        margin-bottom: 20px;
    }
    
    .player-info {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .game-header {
        flex-direction: column;
        gap: 10px;
        padding: 15px;
    }
    
    .header-left,
    .header-right {
        justify-content: center;
    }
    
    .board-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .modal-buttons {
        flex-direction: column;
    }
    
    .score-details {
        flex-direction: column;
    }
}
