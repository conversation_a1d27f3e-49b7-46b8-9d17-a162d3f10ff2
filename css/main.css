/* 圍棋遊戲主樣式 */

/* 全域樣式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', 'Microsoft JhengHei', sans-serif;
    background: linear-gradient(135deg, #8B4513 0%, #D2691E 50%, #F4A460 100%);
    min-height: 100vh;
    color: #2c1810;
    overflow-x: hidden;
}

/* 主容器 */
.main-container {
    background: rgba(255, 255, 255, 0.95);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    max-width: 800px;
    margin: 0 auto;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
}

/* 標題區域 */
.game-header {
    text-align: center;
    padding: 40px 20px;
    background: linear-gradient(135deg, #2c1810, #4a2c1a);
    color: #f5f5dc;
    position: relative;
}

.game-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(245,245,220,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(245,245,220,0.1)"/><circle cx="50" cy="50" r="1" fill="rgba(245,245,220,0.1)"/></svg>');
    opacity: 0.3;
}

.game-title {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    position: relative;
    z-index: 1;
}

.game-subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

/* 主要內容區域 */
.main-content {
    flex: 1;
    padding: 40px 20px;
}

.menu-container {
    max-width: 600px;
    margin: 0 auto;
}

.menu-container h2 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 30px;
    color: #2c1810;
}

.menu-container h3 {
    text-align: center;
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: #4a2c1a;
}

/* 模式選擇 */
.mode-selection {
    display: flex;
    gap: 20px;
    margin-bottom: 40px;
    justify-content: center;
}

.mode-btn {
    flex: 1;
    max-width: 250px;
    padding: 25px;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.mode-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.mode-btn.primary {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
}

.mode-btn.secondary {
    background: linear-gradient(135deg, #50c878, #3ea65c);
    color: white;
}

.btn-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.btn-content h3 {
    font-size: 1.3rem;
    margin-bottom: 8px;
    text-align: center;
}

.btn-content p {
    font-size: 1rem;
    opacity: 0.9;
    text-align: center;
}

/* 棋盤大小選擇 */
.board-size-container,
.ai-difficulty-container {
    text-align: center;
}

.size-options,
.difficulty-options {
    display: flex;
    gap: 30px;
    justify-content: center;
    margin-bottom: 30px;
}

.size-btn,
.difficulty-btn {
    padding: 20px;
    border: 3px solid #8B4513;
    border-radius: 15px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.size-btn:hover,
.difficulty-btn:hover {
    background: #f5f5dc;
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.size-info {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 10px;
}

.size-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #2c1810;
}

.size-label {
    font-size: 1.2rem;
    margin-left: 5px;
    color: #4a2c1a;
}

.size-btn p,
.difficulty-btn p {
    font-size: 0.9rem;
    color: #666;
}

/* AI 難度選擇 */
.difficulty-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 10px;
}

.difficulty-icon {
    font-size: 1.5rem;
}

.difficulty-name {
    font-size: 1.2rem;
    font-weight: bold;
}

.difficulty-btn.easy:hover {
    border-color: #4CAF50;
    background: #e8f5e8;
}

.difficulty-btn.medium:hover {
    border-color: #FF9800;
    background: #fff3e0;
}

.difficulty-btn.hard:hover {
    border-color: #f44336;
    background: #ffebee;
}

.difficulty-btn.expert:hover {
    border-color: #9c27b0;
    background: #f3e5f5;
}

/* AI vs AI 模式樣式 */
.mode-btn.ai-battle {
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    color: white;
    border: 3px solid #ff6b6b;
}

.mode-btn.ai-battle:hover {
    background: linear-gradient(135deg, #ff5252, #26c6da);
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

/* AI vs AI 選擇容器 */
.ai-vs-ai-container {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.ai-selection-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 30px 0;
    gap: 20px;
}

.ai-player-selection {
    flex: 1;
    background: #f8f8f8;
    padding: 20px;
    border-radius: 15px;
    border: 2px solid #ddd;
}

.ai-player-selection h4 {
    margin-top: 0;
    font-size: 1.3rem;
    color: #333;
}

.difficulty-options-small {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin: 15px 0;
}

.difficulty-btn-small {
    padding: 10px;
    border: 2px solid #8B4513;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.difficulty-btn-small:hover {
    background: #f5f5dc;
    transform: scale(1.05);
}

.difficulty-btn-small.selected {
    background: #8B4513;
    color: white;
    font-weight: bold;
}

.difficulty-btn-small.easy.selected {
    background: #4CAF50;
    border-color: #4CAF50;
}

.difficulty-btn-small.medium.selected {
    background: #FF9800;
    border-color: #FF9800;
}

.difficulty-btn-small.hard.selected {
    background: #f44336;
    border-color: #f44336;
}

.difficulty-btn-small.expert.selected {
    background: #9c27b0;
    border-color: #9c27b0;
}

.vs-divider {
    font-size: 2rem;
    font-weight: bold;
    color: #8B4513;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    flex-shrink: 0;
}

.selected-difficulty {
    margin-top: 15px;
    padding: 10px;
    background: #e8f5e8;
    border-radius: 5px;
    font-weight: bold;
    color: #2e7d32;
    border: 1px solid #4caf50;
}

.ai-battle-controls {
    margin-top: 30px;
}

.start-battle-btn {
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 15px;
}

.start-battle-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #ff5252, #26c6da);
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.start-battle-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 返回按鈕 */
.back-btn {
    padding: 12px 25px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* 隱藏類別 */
.hidden {
    display: none !important;
}

/* 遊戲規則 */
.game-rules {
    background: rgba(245, 245, 220, 0.8);
    border-radius: 15px;
    padding: 25px;
    margin-top: 30px;
    border: 2px solid #8B4513;
}

.rules-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.rule-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.rule-icon {
    font-size: 1.2rem;
    min-width: 25px;
}

/* 頁尾 */
.game-footer {
    text-align: center;
    padding: 20px;
    background: #2c1810;
    color: #f5f5dc;
    font-size: 0.9rem;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .mode-selection {
        flex-direction: column;
        align-items: center;
    }
    
    .size-options,
    .difficulty-options {
        flex-direction: column;
        align-items: center;
    }
    
    .game-title {
        font-size: 2.5rem;
    }
    
    .rules-content {
        grid-template-columns: 1fr;
    }
}

/* 動畫效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.menu-container > * {
    animation: fadeIn 0.6s ease-out;
}

.menu-container > *:nth-child(2) { animation-delay: 0.1s; }
.menu-container > *:nth-child(3) { animation-delay: 0.2s; }
.menu-container > *:nth-child(4) { animation-delay: 0.3s; }
