<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>圍棋吃子測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5dc;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-board {
            margin: 20px 0;
            text-align: center;
        }
        canvas {
            border: 2px solid #8B4513;
            border-radius: 5px;
        }
        .test-info {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .test-buttons {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #8B4513;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #A0522D;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>圍棋吃子功能測試</h1>
        
        <div class="test-info">
            <h3>測試說明：</h3>
            <p>1. 點擊棋盤下棋，黑子先行</p>
            <p>2. 當棋子被完全包圍（沒有氣）時應該被吃掉</p>
            <p>3. 被吃掉的棋子會從棋盤上消失</p>
            <p>4. 俘虜數會增加</p>
        </div>
        
        <div class="test-board">
            <canvas id="test-board" width="400" height="400"></canvas>
        </div>
        
        <div class="status">
            <div>當前玩家: <span id="current-player">黑子</span></div>
            <div>黑子俘虜: <span id="black-captures">0</span></div>
            <div>白子俘虜: <span id="white-captures">0</span></div>
            <div>最後訊息: <span id="last-message">開始遊戲</span></div>
        </div>
        
        <div class="test-buttons">
            <button onclick="resetTest()">重置測試</button>
            <button onclick="setupCaptureTest()">設置吃子測試局面</button>
            <button onclick="showGameState()">顯示棋盤狀態</button>
        </div>
        
        <div class="test-info">
            <h3>預設測試局面：</h3>
            <p>點擊「設置吃子測試局面」會在棋盤上放置一個即將被吃的白子，然後黑子可以吃掉它。</p>
        </div>
    </div>

    <!-- 載入圍棋遊戲模組 -->
    <script src="js/go-game.js"></script>
    <script src="js/go-board.js"></script>
    
    <script>
        // 測試用的遊戲實例
        let testGame = new GoGame(9); // 使用 9x9 棋盤方便測試
        let testBoard = new GoBoard(document.getElementById('test-board'), 9);
        
        // 設置點擊事件
        testBoard.canvas.addEventListener('click', function(event) {
            const position = testBoard.getGridPosition(event.clientX, event.clientY);
            if (!position) return;
            
            const [row, col] = position;
            const result = testGame.makeMove(row, col);
            
            if (result.success) {
                // 同步棋盤狀態
                testBoard.setBoardState(testGame.board);
                testBoard.lastMove = [row, col];
                testBoard.drawBoard();
                
                // 更新狀態顯示
                updateStatus(result);
                
                if (result.captured > 0) {
                    document.getElementById('last-message').textContent = 
                        `吃掉了 ${result.captured} 個棋子！`;
                } else {
                    document.getElementById('last-message').textContent = 
                        `在 ${result.notation} 下了一子`;
                }
            } else {
                document.getElementById('last-message').textContent = result.error;
            }
        });
        
        function updateStatus(result) {
            document.getElementById('current-player').textContent = 
                testGame.currentPlayer === 1 ? '黑子' : '白子';
            document.getElementById('black-captures').textContent = 
                testGame.capturedStones.black;
            document.getElementById('white-captures').textContent = 
                testGame.capturedStones.white;
        }
        
        function resetTest() {
            testGame.reset();
            testBoard.clear();
            updateStatus();
            document.getElementById('last-message').textContent = '遊戲重置';
        }
        
        function setupCaptureTest() {
            // 重置遊戲
            resetTest();
            
            // 設置一個簡單的吃子局面
            // 白子在中央，被黑子包圍，只剩一口氣
            testGame.board[4][4] = 2; // 白子
            testGame.board[3][4] = 1; // 黑子
            testGame.board[5][4] = 1; // 黑子
            testGame.board[4][3] = 1; // 黑子
            // [4][5] 位置空著，是白子的最後一口氣
            
            // 設置當前玩家為黑子，準備吃掉白子
            testGame.currentPlayer = 1;
            
            // 同步棋盤顯示
            testBoard.setBoardState(testGame.board);
            testBoard.drawBoard();
            
            updateStatus();
            document.getElementById('last-message').textContent = 
                '測試局面已設置！黑子下在 (4,5) 可以吃掉白子';
        }
        
        function showGameState() {
            console.log('棋盤狀態:', testGame.board);
            console.log('當前玩家:', testGame.currentPlayer);
            console.log('俘虜數:', testGame.capturedStones);
            
            let boardStr = '';
            for (let row = 0; row < 9; row++) {
                let rowStr = '';
                for (let col = 0; col < 9; col++) {
                    const stone = testGame.board[row][col];
                    rowStr += stone === 0 ? '.' : (stone === 1 ? '●' : '○');
                }
                boardStr += rowStr + '\n';
            }
            console.log('棋盤圖示:\n' + boardStr);
            
            document.getElementById('last-message').textContent = 
                '棋盤狀態已輸出到控制台';
        }
        
        // 初始化
        updateStatus();
    </script>
</body>
</html>
