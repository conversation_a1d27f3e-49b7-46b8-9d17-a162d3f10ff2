/**
 * 圍棋遊戲邏輯類別
 * 處理遊戲規則、吃子、計分等核心邏輯
 */

class GoGame {
    /**
     * 建構函式
     * @param {number} size - 棋盤大小
     */
    constructor(size = 19) {
        this.size = size;
        this.board = Array(size).fill().map(() => Array(size).fill(0));
        this.currentPlayer = 1; // 1=黑子, 2=白子
        this.moveHistory = [];
        this.capturedStones = { black: 0, white: 0 };
        this.passCount = 0;
        this.gameEnded = false;
        this.komi = 6.5; // 貼目
        
        // 劫爭檢測
        this.koPosition = null;
        this.boardHistory = [];
    }

    /**
     * 檢查位置是否有效
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {boolean} 是否有效
     */
    isValidPosition(row, col) {
        return row >= 0 && row < this.size && col >= 0 && col < this.size;
    }

    /**
     * 取得相鄰位置
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {Array} 相鄰位置陣列
     */
    getAdjacentPositions(row, col) {
        const directions = [[-1, 0], [1, 0], [0, -1], [0, 1]];
        return directions
            .map(([dr, dc]) => [row + dr, col + dc])
            .filter(([r, c]) => this.isValidPosition(r, c));
    }

    /**
     * 取得連通的棋子群組
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @param {number} color - 棋子顏色
     * @returns {Array} 連通群組的位置陣列
     */
    getGroup(row, col, color) {
        const visited = new Set();
        const group = [];
        const stack = [[row, col]];
        
        while (stack.length > 0) {
            const [r, c] = stack.pop();
            const key = `${r},${c}`;
            
            if (visited.has(key)) continue;
            visited.add(key);
            
            if (this.board[r][c] === color) {
                group.push([r, c]);
                
                this.getAdjacentPositions(r, c).forEach(([nr, nc]) => {
                    const nkey = `${nr},${nc}`;
                    if (!visited.has(nkey) && this.board[nr][nc] === color) {
                        stack.push([nr, nc]);
                    }
                });
            }
        }
        
        return group;
    }

    /**
     * 計算群組的氣數
     * @param {Array} group - 棋子群組
     * @returns {number} 氣數
     */
    getLiberties(group) {
        const liberties = new Set();
        
        group.forEach(([row, col]) => {
            this.getAdjacentPositions(row, col).forEach(([r, c]) => {
                if (this.board[r][c] === 0) {
                    liberties.add(`${r},${c}`);
                }
            });
        });
        
        return liberties.size;
    }

    /**
     * 移除棋子群組
     * @param {Array} group - 要移除的群組
     * @returns {number} 移除的棋子數量
     */
    removeGroup(group) {
        group.forEach(([row, col]) => {
            this.board[row][col] = 0;
        });
        return group.length;
    }

    /**
     * 檢查並移除被吃的棋子
     * @param {number} opponentColor - 對手顏色
     * @returns {number} 被吃的棋子數量
     */
    checkCaptures(opponentColor) {
        let totalCaptured = 0;
        const visited = new Set();
        
        for (let row = 0; row < this.size; row++) {
            for (let col = 0; col < this.size; col++) {
                const key = `${row},${col}`;
                if (this.board[row][col] === opponentColor && !visited.has(key)) {
                    const group = this.getGroup(row, col, opponentColor);
                    group.forEach(([r, c]) => visited.add(`${r},${c}`));
                    
                    if (this.getLiberties(group) === 0) {
                        totalCaptured += this.removeGroup(group);
                    }
                }
            }
        }
        
        return totalCaptured;
    }

    /**
     * 檢查是否為自殺手
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @param {number} color - 棋子顏色
     * @returns {boolean} 是否為自殺手
     */
    isSuicideMove(row, col, color) {
        // 暫時放置棋子
        this.board[row][col] = color;
        
        // 檢查是否能吃掉對手棋子
        const opponentColor = color === 1 ? 2 : 1;
        let canCapture = false;
        
        this.getAdjacentPositions(row, col).forEach(([r, c]) => {
            if (this.board[r][c] === opponentColor) {
                const group = this.getGroup(r, c, opponentColor);
                if (this.getLiberties(group) === 0) {
                    canCapture = true;
                }
            }
        });
        
        // 檢查自己的群組是否有氣
        const myGroup = this.getGroup(row, col, color);
        const hasLiberties = this.getLiberties(myGroup) > 0;
        
        // 移除暫時放置的棋子
        this.board[row][col] = 0;
        
        // 如果能吃子或有氣，則不是自殺手
        return !canCapture && !hasLiberties;
    }

    /**
     * 檢查是否違反劫爭規則
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {boolean} 是否違反劫爭規則
     */
    isKoViolation(row, col) {
        return this.koPosition && 
               this.koPosition[0] === row && 
               this.koPosition[1] === col;
    }

    /**
     * 下棋
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {Object} 下棋結果
     */
    makeMove(row, col) {
        // 檢查位置是否有效
        if (!this.isValidPosition(row, col)) {
            return { success: false, error: '無效位置' };
        }
        
        // 檢查位置是否已被佔用
        if (this.board[row][col] !== 0) {
            return { success: false, error: '位置已被佔用' };
        }
        
        // 檢查是否違反劫爭規則
        if (this.isKoViolation(row, col)) {
            return { success: false, error: '違反劫爭規則' };
        }
        
        // 檢查是否為自殺手
        if (this.isSuicideMove(row, col, this.currentPlayer)) {
            return { success: false, error: '不能自殺' };
        }
        
        // 保存當前棋盤狀態
        const previousBoard = this.board.map(row => [...row]);
        
        // 放置棋子
        this.board[row][col] = this.currentPlayer;
        
        // 檢查並移除被吃的棋子
        const opponentColor = this.currentPlayer === 1 ? 2 : 1;
        const capturedCount = this.checkCaptures(opponentColor);
        
        // 更新俘虜計數
        if (this.currentPlayer === 1) {
            this.capturedStones.black += capturedCount;
        } else {
            this.capturedStones.white += capturedCount;
        }
        
        // 檢查劫爭
        this.updateKoPosition(capturedCount, row, col);
        
        // 記錄棋步
        this.moveHistory.push({
            row,
            col,
            color: this.currentPlayer,
            captured: capturedCount,
            notation: this.coordinateToNotation(row, col)
        });
        
        // 記錄棋盤歷史
        this.boardHistory.push(previousBoard);
        
        // 重置 pass 計數
        this.passCount = 0;
        
        // 切換玩家
        this.currentPlayer = this.currentPlayer === 1 ? 2 : 1;
        
        return {
            success: true,
            captured: capturedCount,
            notation: this.coordinateToNotation(row, col)
        };
    }

    /**
     * 更新劫爭位置
     * @param {number} capturedCount - 被吃棋子數量
     * @param {number} row - 當前下棋位置行
     * @param {number} col - 當前下棋位置列
     */
    updateKoPosition(capturedCount, row, col) {
        this.koPosition = null;
        
        // 只有吃掉一個子時才可能形成劫爭
        if (capturedCount === 1) {
            // 檢查是否為單子被吃的情況
            const adjacentPositions = this.getAdjacentPositions(row, col);
            const emptyAdjacent = adjacentPositions.filter(([r, c]) => this.board[r][c] === 0);
            
            if (emptyAdjacent.length === 1) {
                this.koPosition = emptyAdjacent[0];
            }
        }
    }

    /**
     * Pass
     * @returns {Object} Pass 結果
     */
    pass() {
        this.passCount++;
        this.currentPlayer = this.currentPlayer === 1 ? 2 : 1;
        
        this.moveHistory.push({
            pass: true,
            color: this.currentPlayer === 1 ? 2 : 1
        });
        
        // 雙方都 pass 則遊戲結束
        if (this.passCount >= 2) {
            this.gameEnded = true;
            return { success: true, gameEnded: true };
        }
        
        return { success: true, gameEnded: false };
    }

    /**
     * 座標轉換為棋譜記號
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {string} 棋譜記號
     */
    coordinateToNotation(row, col) {
        const letters = 'ABCDEFGHJKLMNOPQRST';
        const letter = letters[col];
        const number = this.size - row;
        return letter + number;
    }

    /**
     * 取得遊戲狀態
     * @returns {Object} 遊戲狀態
     */
    getGameState() {
        return {
            board: this.board.map(row => [...row]),
            currentPlayer: this.currentPlayer,
            capturedStones: { ...this.capturedStones },
            moveHistory: [...this.moveHistory],
            gameEnded: this.gameEnded,
            passCount: this.passCount
        };
    }

    /**
     * 重置遊戲
     */
    reset() {
        this.board = Array(this.size).fill().map(() => Array(this.size).fill(0));
        this.currentPlayer = 1;
        this.moveHistory = [];
        this.capturedStones = { black: 0, white: 0 };
        this.passCount = 0;
        this.gameEnded = false;
        this.koPosition = null;
        this.boardHistory = [];
    }
}
