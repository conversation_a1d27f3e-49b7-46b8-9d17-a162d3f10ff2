/**
 * 圍棋棋盤類別
 * 處理棋盤繪製、座標轉換和基本棋盤操作
 */

class GoBoard {
    /**
     * 建構函式
     * @param {HTMLCanvasElement} canvas - 畫布元素
     * @param {number} size - 棋盤大小 (13 或 19)
     */
    constructor(canvas, size = 19) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.size = size;
        this.cellSize = 0;
        this.margin = 30;
        this.boardSize = 0;
        
        // 棋盤狀態：0=空, 1=黑子, 2=白子
        this.board = Array(size).fill().map(() => Array(size).fill(0));
        
        // 最後一手的位置
        this.lastMove = null;
        
        // 星位座標
        this.starPoints = this.getStarPoints();
        
        this.initializeCanvas();
        this.drawBoard();
    }

    /**
     * 初始化畫布
     */
    initializeCanvas() {
        const containerWidth = Math.min(600, window.innerWidth - 100);
        this.boardSize = containerWidth - this.margin * 2;
        this.cellSize = this.boardSize / (this.size - 1);
        
        this.canvas.width = containerWidth;
        this.canvas.height = containerWidth;
        this.canvas.style.width = containerWidth + 'px';
        this.canvas.style.height = containerWidth + 'px';
    }

    /**
     * 取得星位座標
     * @returns {Array} 星位座標陣列
     */
    getStarPoints() {
        if (this.size === 19) {
            return [
                [3, 3], [3, 9], [3, 15],
                [9, 3], [9, 9], [9, 15],
                [15, 3], [15, 9], [15, 15]
            ];
        } else if (this.size === 13) {
            return [
                [3, 3], [3, 9],
                [6, 6],
                [9, 3], [9, 9]
            ];
        }
        return [];
    }

    /**
     * 繪製棋盤
     */
    drawBoard() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 繪製木紋背景
        this.drawWoodBackground();
        
        // 繪製網格線
        this.drawGrid();
        
        // 繪製星位
        this.drawStarPoints();
        
        // 繪製座標標籤
        this.drawCoordinates();
        
        // 繪製棋子
        this.drawStones();
        
        // 標示最後一手
        if (this.lastMove) {
            this.highlightLastMove();
        }
    }

    /**
     * 繪製木紋背景
     */
    drawWoodBackground() {
        const gradient = this.ctx.createLinearGradient(0, 0, this.canvas.width, this.canvas.height);
        gradient.addColorStop(0, '#DEB887');
        gradient.addColorStop(0.5, '#D2B48C');
        gradient.addColorStop(1, '#CD853F');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 添加木紋紋理
        this.ctx.strokeStyle = 'rgba(139, 69, 19, 0.1)';
        this.ctx.lineWidth = 1;
        for (let i = 0; i < 20; i++) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, i * 30);
            this.ctx.lineTo(this.canvas.width, i * 30 + Math.sin(i) * 10);
            this.ctx.stroke();
        }
    }

    /**
     * 繪製網格線
     */
    drawGrid() {
        this.ctx.strokeStyle = '#2c1810';
        this.ctx.lineWidth = 1;
        
        // 繪製垂直線
        for (let i = 0; i < this.size; i++) {
            const x = this.margin + i * this.cellSize;
            this.ctx.beginPath();
            this.ctx.moveTo(x, this.margin);
            this.ctx.lineTo(x, this.margin + this.boardSize);
            this.ctx.stroke();
        }
        
        // 繪製水平線
        for (let i = 0; i < this.size; i++) {
            const y = this.margin + i * this.cellSize;
            this.ctx.beginPath();
            this.ctx.moveTo(this.margin, y);
            this.ctx.lineTo(this.margin + this.boardSize, y);
            this.ctx.stroke();
        }
    }

    /**
     * 繪製星位
     */
    drawStarPoints() {
        this.ctx.fillStyle = '#2c1810';
        
        this.starPoints.forEach(([row, col]) => {
            const x = this.margin + col * this.cellSize;
            const y = this.margin + row * this.cellSize;
            
            this.ctx.beginPath();
            this.ctx.arc(x, y, 4, 0, 2 * Math.PI);
            this.ctx.fill();
        });
    }

    /**
     * 繪製座標標籤
     */
    drawCoordinates() {
        this.ctx.fillStyle = '#2c1810';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        
        // 繪製列標籤 (A-T, 跳過 I)
        const letters = 'ABCDEFGHJKLMNOPQRST';
        for (let i = 0; i < this.size; i++) {
            const x = this.margin + i * this.cellSize;
            this.ctx.fillText(letters[i], x, 15);
            this.ctx.fillText(letters[i], x, this.canvas.height - 15);
        }
        
        // 繪製行標籤 (1-19)
        for (let i = 0; i < this.size; i++) {
            const y = this.margin + i * this.cellSize;
            const number = this.size - i;
            this.ctx.fillText(number.toString(), 15, y);
            this.ctx.fillText(number.toString(), this.canvas.width - 15, y);
        }
    }

    /**
     * 繪製棋子
     */
    drawStones() {
        const stoneRadius = this.cellSize * 0.4;
        
        for (let row = 0; row < this.size; row++) {
            for (let col = 0; col < this.size; col++) {
                const stone = this.board[row][col];
                if (stone !== 0) {
                    const x = this.margin + col * this.cellSize;
                    const y = this.margin + row * this.cellSize;
                    
                    this.drawStone(x, y, stone, stoneRadius);
                }
            }
        }
    }

    /**
     * 繪製單個棋子
     * @param {number} x - X 座標
     * @param {number} y - Y 座標
     * @param {number} color - 棋子顏色 (1=黑, 2=白)
     * @param {number} radius - 棋子半徑
     */
    drawStone(x, y, color, radius) {
        // 繪製陰影
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.beginPath();
        this.ctx.arc(x + 2, y + 2, radius, 0, 2 * Math.PI);
        this.ctx.fill();
        
        if (color === 1) {
            // 黑子
            const gradient = this.ctx.createRadialGradient(x - radius/3, y - radius/3, 0, x, y, radius);
            gradient.addColorStop(0, '#666');
            gradient.addColorStop(1, '#000');
            this.ctx.fillStyle = gradient;
        } else {
            // 白子
            const gradient = this.ctx.createRadialGradient(x - radius/3, y - radius/3, 0, x, y, radius);
            gradient.addColorStop(0, '#fff');
            gradient.addColorStop(1, '#ddd');
            this.ctx.fillStyle = gradient;
        }
        
        this.ctx.beginPath();
        this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
        this.ctx.fill();
        
        // 棋子邊框
        this.ctx.strokeStyle = color === 1 ? '#333' : '#999';
        this.ctx.lineWidth = 1;
        this.ctx.stroke();
    }

    /**
     * 標示最後一手
     */
    highlightLastMove() {
        if (!this.lastMove) return;
        
        const [row, col] = this.lastMove;
        const x = this.margin + col * this.cellSize;
        const y = this.margin + row * this.cellSize;
        
        this.ctx.strokeStyle = '#ff0000';
        this.ctx.lineWidth = 3;
        this.ctx.beginPath();
        this.ctx.arc(x, y, this.cellSize * 0.2, 0, 2 * Math.PI);
        this.ctx.stroke();
    }

    /**
     * 滑鼠座標轉換為棋盤座標
     * @param {number} mouseX - 滑鼠 X 座標
     * @param {number} mouseY - 滑鼠 Y 座標
     * @returns {Array|null} 棋盤座標 [row, col] 或 null
     */
    getGridPosition(mouseX, mouseY) {
        const rect = this.canvas.getBoundingClientRect();
        const x = mouseX - rect.left;
        const y = mouseY - rect.top;
        
        const col = Math.round((x - this.margin) / this.cellSize);
        const row = Math.round((y - this.margin) / this.cellSize);
        
        if (row >= 0 && row < this.size && col >= 0 && col < this.size) {
            return [row, col];
        }
        
        return null;
    }

    /**
     * 放置棋子
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @param {number} color - 棋子顏色 (1=黑, 2=白)
     * @returns {boolean} 是否成功放置
     */
    placeStone(row, col, color) {
        if (this.board[row][col] !== 0) {
            return false; // 位置已被佔用
        }
        
        this.board[row][col] = color;
        this.lastMove = [row, col];
        this.drawBoard();
        return true;
    }

    /**
     * 移除棋子
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     */
    removeStone(row, col) {
        this.board[row][col] = 0;
        this.drawBoard();
    }

    /**
     * 清空棋盤
     */
    clear() {
        this.board = Array(this.size).fill().map(() => Array(this.size).fill(0));
        this.lastMove = null;
        this.drawBoard();
    }

    /**
     * 取得棋盤狀態
     * @returns {Array} 棋盤狀態陣列
     */
    getBoardState() {
        return this.board.map(row => [...row]);
    }

    /**
     * 設置棋盤狀態
     * @param {Array} boardState - 棋盤狀態陣列
     */
    setBoardState(boardState) {
        this.board = boardState.map(row => [...row]);
        this.drawBoard();
    }

    /**
     * 座標轉換為棋譜記號
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {string} 棋譜記號 (如 "D4")
     */
    coordinateToNotation(row, col) {
        const letters = 'ABCDEFGHJKLMNOPQRST';
        const letter = letters[col];
        const number = this.size - row;
        return letter + number;
    }

    /**
     * 顯示領域標記
     * @param {Array} territoryMap - 領域地圖
     */
    showTerritoryMarks(territoryMap) {
        if (!territoryMap) return;

        const markSize = this.cellSize * 0.15;

        for (let row = 0; row < this.size; row++) {
            for (let col = 0; col < this.size; col++) {
                const territory = territoryMap[row][col];
                if (territory !== 0 && this.board[row][col] === 0) {
                    const x = this.margin + col * this.cellSize;
                    const y = this.margin + row * this.cellSize;

                    this.ctx.fillStyle = territory === 1 ? 'rgba(0, 0, 0, 0.3)' : 'rgba(255, 255, 255, 0.7)';
                    this.ctx.strokeStyle = territory === 1 ? '#000' : '#666';
                    this.ctx.lineWidth = 1;

                    this.ctx.beginPath();
                    this.ctx.rect(x - markSize, y - markSize, markSize * 2, markSize * 2);
                    this.ctx.fill();
                    this.ctx.stroke();
                }
            }
        }
    }

    /**
     * 清除領域標記
     */
    clearTerritoryMarks() {
        this.drawBoard();
    }

    /**
     * 調整畫布大小
     */
    resize() {
        this.initializeCanvas();
        this.drawBoard();
    }
}
