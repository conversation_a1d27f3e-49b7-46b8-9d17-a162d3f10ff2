/**
 * 圍棋遊戲控制器
 * 協調棋盤、遊戲邏輯、AI 和 UI 的交互
 */

class GameController {
    /**
     * 建構函式
     */
    constructor() {
        this.board = null;
        this.game = null;
        this.ai = null;
        this.blackAi = null; // AI vs AI 模式的黑子 AI
        this.whiteAi = null; // AI vs AI 模式的白子 AI
        this.gameMode = null;
        this.boardSize = 19;
        this.aiDifficulty = 'medium';
        this.blackAiDifficulty = 'medium';
        this.whiteAiDifficulty = 'medium';
        this.isAiThinking = false;
        this.showingTerritory = false;
        this.aiVsAiSpeed = 1000; // AI vs AI 模式的下棋間隔
        this.aiVsAiPaused = false; // AI vs AI 是否暫停
        this.aiVsAiStepMode = false; // AI vs AI 單步模式

        this.init();
    }

    /**
     * 初始化遊戲控制器
     */
    init() {
        // 等待 DOM 載入完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupGame();
            });
        } else {
            this.setupGame();
        }
    }

    /**
     * 設置遊戲
     */
    setupGame() {
        // 從 URL 參數取得遊戲設定
        this.parseGameSettings();
        
        // 初始化遊戲組件
        this.initializeComponents();
        
        // 設置事件監聽器
        this.setupEventListeners();
        
        // 更新 UI
        this.updateUI();
        
        console.log('圍棋遊戲初始化完成');
        console.log(`模式: ${this.gameMode}, 棋盤: ${this.boardSize}路`);
        if (this.gameMode === 'single') {
            console.log(`AI 難度: ${this.aiDifficulty}`);
        } else if (this.gameMode === 'ai-vs-ai') {
            console.log(`黑子 AI: ${this.blackAiDifficulty}, 白子 AI: ${this.whiteAiDifficulty}`);
            // AI vs AI 模式自動開始
            setTimeout(() => this.makeAiVsAiMove(), 1000);
        }
    }

    /**
     * 解析遊戲設定
     */
    parseGameSettings() {
        const urlParams = new URLSearchParams(window.location.search);
        this.gameMode = urlParams.get('mode') || 'single';
        this.boardSize = parseInt(urlParams.get('size')) || 19;
        this.aiDifficulty = urlParams.get('difficulty') || 'medium';
        this.blackAiDifficulty = urlParams.get('blackAi') || 'medium';
        this.whiteAiDifficulty = urlParams.get('whiteAi') || 'medium';
    }

    /**
     * 初始化遊戲組件
     */
    initializeComponents() {
        // 初始化棋盤
        const canvas = document.getElementById('go-board');
        this.board = new GoBoard(canvas, this.boardSize);

        // 初始化遊戲邏輯
        this.game = new GoGame(this.boardSize);

        // 初始化 AI
        if (this.gameMode === 'single') {
            this.ai = new GoAI(this.aiDifficulty, 2, this.boardSize); // AI 執白子
        } else if (this.gameMode === 'ai-vs-ai') {
            this.blackAi = new GoAI(this.blackAiDifficulty, 1, this.boardSize); // 黑子 AI
            this.whiteAi = new GoAI(this.whiteAiDifficulty, 2, this.boardSize); // 白子 AI
        }
    }

    /**
     * 設置事件監聽器
     */
    setupEventListeners() {
        // 棋盤點擊事件
        this.board.canvas.addEventListener('click', (event) => {
            this.handleBoardClick(event);
        });

        // 控制按鈕事件
        this.setupControlButtons();
        
        // 視窗大小調整事件
        window.addEventListener('resize', () => {
            this.board.resize();
        });
    }

    /**
     * 設置控制按鈕
     */
    setupControlButtons() {
        // Pass 按鈕
        const passBtn = document.getElementById('pass-btn');
        if (passBtn) {
            passBtn.addEventListener('click', () => {
                this.handlePass();
            });
        }

        // 認輸按鈕
        const resignBtn = document.getElementById('resign-btn');
        if (resignBtn) {
            resignBtn.addEventListener('click', () => {
                this.handleResign();
            });
        }

        // 悔棋按鈕
        const undoBtn = document.getElementById('undo-btn');
        if (undoBtn) {
            undoBtn.addEventListener('click', () => {
                this.handleUndo();
            });
        }

        // 新局按鈕
        const newGameBtn = document.getElementById('new-game-btn');
        if (newGameBtn) {
            newGameBtn.addEventListener('click', () => {
                this.startNewGame();
            });
        }

        // 返回選單按鈕
        const backToMenuBtn = document.getElementById('back-to-menu-btn');
        if (backToMenuBtn) {
            backToMenuBtn.addEventListener('click', () => {
                window.location.href = 'index.html';
            });
        }

        // 分析按鈕（顯示領域）
        const analyzeBtn = document.getElementById('analyze-btn');
        if (analyzeBtn) {
            analyzeBtn.addEventListener('click', () => {
                this.toggleTerritoryDisplay();
            });
        }

        // AI vs AI 控制按鈕
        this.setupAiVsAiControls();
    }

    /**
     * 設置 AI vs AI 控制按鈕
     */
    setupAiVsAiControls() {
        // 顯示/隱藏 AI vs AI 控制面板
        const aiVsAiControls = document.getElementById('ai-vs-ai-controls');
        if (aiVsAiControls) {
            if (this.gameMode === 'ai-vs-ai') {
                aiVsAiControls.classList.remove('hidden');
            } else {
                aiVsAiControls.classList.add('hidden');
            }
        }

        // 速度控制
        const speedSelect = document.getElementById('ai-battle-speed');
        if (speedSelect) {
            speedSelect.addEventListener('change', (e) => {
                this.aiVsAiSpeed = parseInt(e.target.value);
                console.log(`AI 對戰速度設為: ${this.aiVsAiSpeed}ms`);
            });
        }

        // 暫停/繼續按鈕
        const pauseBtn = document.getElementById('pause-ai-battle-btn');
        if (pauseBtn) {
            pauseBtn.addEventListener('click', () => {
                this.toggleAiVsAiPause();
            });
        }

        // 單步按鈕
        const stepBtn = document.getElementById('step-ai-battle-btn');
        if (stepBtn) {
            stepBtn.addEventListener('click', () => {
                this.stepAiVsAi();
            });
        }
    }

    /**
     * 切換 AI vs AI 暫停狀態
     */
    toggleAiVsAiPause() {
        this.aiVsAiPaused = !this.aiVsAiPaused;
        const pauseBtn = document.getElementById('pause-ai-battle-btn');

        if (pauseBtn) {
            pauseBtn.textContent = this.aiVsAiPaused ? '繼續' : '暫停';
        }

        console.log(`AI 對戰 ${this.aiVsAiPaused ? '暫停' : '繼續'}`);
    }

    /**
     * AI vs AI 單步執行
     */
    stepAiVsAi() {
        if (this.gameMode === 'ai-vs-ai' && !this.game.gameEnded) {
            this.aiVsAiStepMode = true;
            this.makeAiVsAiMove();
        }
    }

    /**
     * 切換領域顯示
     */
    toggleTerritoryDisplay() {
        if (!this.showingTerritory) {
            // 顯示領域
            const score = this.game.calculateFinalScore();
            this.board.showTerritoryMarks(score.territoryMap);
            this.showingTerritory = true;

            // 更新按鈕文字
            const analyzeBtn = document.getElementById('analyze-btn');
            if (analyzeBtn) {
                analyzeBtn.textContent = '隱藏分析';
            }
        } else {
            // 隱藏領域
            this.board.clearTerritoryMarks();
            this.showingTerritory = false;

            // 更新按鈕文字
            const analyzeBtn = document.getElementById('analyze-btn');
            if (analyzeBtn) {
                analyzeBtn.textContent = '分析';
            }
        }
    }

    /**
     * 處理棋盤點擊
     * @param {Event} event - 點擊事件
     */
    async handleBoardClick(event) {
        // AI vs AI 模式下禁用人工下棋
        if (this.gameMode === 'ai-vs-ai') {
            return;
        }

        if (this.game.gameEnded || this.isAiThinking) {
            return;
        }

        const position = this.board.getGridPosition(event.clientX, event.clientY);
        if (!position) {
            return;
        }

        const [row, col] = position;
        await this.makeMove(row, col);
    }

    /**
     * 下棋
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     */
    async makeMove(row, col) {
        const result = this.game.makeMove(row, col);
        
        if (result.success) {
            // 同步棋盤狀態（包含被吃掉的棋子）
            this.board.setBoardState(this.game.board);
            this.board.lastMove = [row, col];
            this.board.drawBoard();

            // 更新 UI
            this.updateUI();
            this.updateMoveHistory(result);
            
            // 檢查遊戲是否結束
            if (this.game.gameEnded) {
                this.endGame();
                return;
            }

            // 處理 AI 回合
            if (this.gameMode === 'single' && this.game.currentPlayer === 2) {
                await this.makeAiMove();
            } else if (this.gameMode === 'ai-vs-ai') {
                // AI vs AI 模式，延遲後讓下一個 AI 下棋
                setTimeout(() => this.makeAiVsAiMove(), this.aiVsAiSpeed);
            }
        } else {
            // 顯示錯誤訊息
            this.showMessage(result.error);
        }
    }

    /**
     * AI 下棋
     */
    async makeAiMove() {
        if (!this.ai || this.game.gameEnded) {
            return;
        }

        this.isAiThinking = true;
        this.showAiThinking(true);

        try {
            const aiMove = await this.ai.chooseMove(this.game);

            if (aiMove.pass) {
                this.handlePass();
            } else {
                const result = this.game.makeMove(aiMove.row, aiMove.col);
                if (result.success) {
                    // 同步棋盤狀態（包含被吃掉的棋子）
                    this.board.setBoardState(this.game.board);
                    this.board.lastMove = [aiMove.row, aiMove.col];
                    this.board.drawBoard();
                    this.updateUI();
                    this.updateMoveHistory(result);

                    if (this.game.gameEnded) {
                        this.endGame();
                    }
                }
            }
        } catch (error) {
            console.error('AI 下棋錯誤:', error);
        } finally {
            this.isAiThinking = false;
            this.showAiThinking(false);
        }
    }

    /**
     * AI vs AI 模式下棋
     */
    async makeAiVsAiMove() {
        if (this.game.gameEnded) {
            return;
        }

        // 檢查是否暫停（單步模式除外）
        if (this.aiVsAiPaused && !this.aiVsAiStepMode) {
            setTimeout(() => this.makeAiVsAiMove(), 100);
            return;
        }

        const currentAi = this.game.currentPlayer === 1 ? this.blackAi : this.whiteAi;
        const aiName = this.game.currentPlayer === 1 ? '黑子 AI' : '白子 AI';

        this.isAiThinking = true;
        this.showAiThinking(true);

        try {
            const aiMove = await currentAi.chooseMove(this.game);

            if (aiMove.pass) {
                this.handlePass();
            } else {
                const result = this.game.makeMove(aiMove.row, aiMove.col);
                if (result.success) {
                    // 同步棋盤狀態
                    this.board.setBoardState(this.game.board);
                    this.board.lastMove = [aiMove.row, aiMove.col];
                    this.board.drawBoard();
                    this.updateUI();
                    this.updateMoveHistory(result);

                    console.log(`${aiName} 下在 ${result.notation}`);

                    if (this.game.gameEnded) {
                        this.endGame();
                        return;
                    }

                    // 重置單步模式
                    this.aiVsAiStepMode = false;

                    // 繼續下一個 AI 的回合（如果沒有暫停）
                    if (!this.aiVsAiPaused) {
                        setTimeout(() => this.makeAiVsAiMove(), this.aiVsAiSpeed);
                    }
                }
            }
        } catch (error) {
            console.error(`${aiName} 下棋錯誤:`, error);
        } finally {
            this.isAiThinking = false;
            this.showAiThinking(false);
        }
    }

    /**
     * 處理 Pass
     */
    handlePass() {
        const result = this.game.pass();
        this.updateUI();
        this.updateMoveHistory({ pass: true });
        
        if (result.gameEnded) {
            this.endGame();
        } else if (this.gameMode === 'single' && this.game.currentPlayer === 2) {
            // AI 的回合
            setTimeout(() => this.makeAiMove(), 500);
        }
    }

    /**
     * 處理認輸
     */
    handleResign() {
        const winner = this.game.currentPlayer === 1 ? '白子' : '黑子';
        this.showGameEndModal(`${winner}獲勝！`, '對手認輸');
    }

    /**
     * 處理悔棋
     */
    handleUndo() {
        // 簡單實現：重置到上一步
        if (this.game.moveHistory.length > 0) {
            // 這裡需要實現更複雜的悔棋邏輯
            console.log('悔棋功能待實現');
        }
    }

    /**
     * 開始新局
     */
    startNewGame() {
        this.game.reset();
        this.board.clear();
        this.updateUI();
        this.hideGameEndModal();
    }

    /**
     * 結束遊戲
     */
    endGame() {
        const score = this.calculateFinalScore();
        const winner = score.blackTotal > score.whiteTotal ? '黑子' : '白子';
        this.showGameEndModal(`${winner}獲勝！`, score);
    }

    /**
     * 計算最終分數
     * @returns {Object} 分數詳情
     */
    calculateFinalScore() {
        return this.game.calculateFinalScore();
    }

    /**
     * 更新 UI
     */
    updateUI() {
        // 更新遊戲資訊
        const modeText = {
            'single': '單機版',
            'two-player': '雙人對戰',
            'ai-vs-ai': 'AI 對戰'
        };
        document.getElementById('game-mode').textContent = modeText[this.gameMode] || this.gameMode;
        document.getElementById('board-size').textContent = `${this.boardSize}路`;

        // 更新玩家資訊
        document.getElementById('black-captures').textContent = this.game.capturedStones.black;
        document.getElementById('white-captures').textContent = this.game.capturedStones.white;

        // 更新當前玩家
        const currentPlayerText = this.game.currentPlayer === 1 ? '黑子下棋' : '白子下棋';
        document.getElementById('current-player').textContent = currentPlayerText;

        // 更新玩家名稱
        if (this.gameMode === 'single') {
            document.getElementById('black-player-name').textContent = '玩家 (黑)';
            document.getElementById('white-player-name').textContent = `AI (白) - ${this.getDifficultyText()}`;
        } else if (this.gameMode === 'ai-vs-ai') {
            document.getElementById('black-player-name').textContent = `AI (黑) - ${this.getDifficultyText(this.blackAiDifficulty)}`;
            document.getElementById('white-player-name').textContent = `AI (白) - ${this.getDifficultyText(this.whiteAiDifficulty)}`;
        } else {
            document.getElementById('black-player-name').textContent = '玩家一 (黑)';
            document.getElementById('white-player-name').textContent = '玩家二 (白)';
        }

        // 更新即時分數（如果遊戲進行中且有足夠棋子）
        if (this.game.moveCount > 10) {
            this.updateCurrentScore();
        }
    }

    /**
     * 更新當前分數顯示
     */
    updateCurrentScore() {
        try {
            const score = this.game.calculateFinalScore();

            // 更新領域顯示
            const blackTerritoryElement = document.getElementById('black-territory');
            const whiteTerritoryElement = document.getElementById('white-territory');

            if (blackTerritoryElement) {
                blackTerritoryElement.textContent = score.blackTerritory;
            }
            if (whiteTerritoryElement) {
                whiteTerritoryElement.textContent = score.whiteTerritory;
            }
        } catch (error) {
            // 如果計算分數時出錯，不影響遊戲進行
            console.warn('計算即時分數時出錯:', error);
        }
    }

    /**
     * 更新棋譜記錄
     * @param {Object} moveResult - 下棋結果
     */
    updateMoveHistory(moveResult) {
        const moveList = document.getElementById('move-list');
        const moveNumber = this.game.moveHistory.length;
        
        let moveText;
        if (moveResult.pass) {
            moveText = `${moveNumber}. Pass`;
        } else {
            moveText = `${moveNumber}. ${moveResult.notation}`;
            if (moveResult.captured > 0) {
                moveText += ` (吃${moveResult.captured}子)`;
            }
        }
        
        const moveElement = document.createElement('div');
        moveElement.textContent = moveText;
        moveList.appendChild(moveElement);
        moveList.scrollTop = moveList.scrollHeight;
    }

    /**
     * 顯示 AI 思考狀態
     * @param {boolean} show - 是否顯示
     */
    showAiThinking(show) {
        const aiThinking = document.getElementById('ai-thinking');
        if (aiThinking) {
            aiThinking.classList.toggle('hidden', !show);
        }
    }

    /**
     * 顯示訊息
     * @param {string} message - 訊息內容
     */
    showMessage(message) {
        // 簡單的訊息顯示
        alert(message);
    }

    /**
     * 顯示遊戲結束對話框
     * @param {string} result - 遊戲結果
     * @param {Object} score - 分數詳情
     */
    showGameEndModal(result, score) {
        const modal = document.getElementById('game-end-modal');
        const resultElement = document.getElementById('game-result');
        
        resultElement.textContent = result;
        
        if (typeof score === 'object') {
            // 更新分數顯示
            document.getElementById('final-black-territory').textContent = score.blackTerritory;
            document.getElementById('final-white-territory').textContent = score.whiteTerritory;
            document.getElementById('final-black-captures').textContent = score.blackCaptures;
            document.getElementById('final-white-captures').textContent = score.whiteCaptures;
            document.getElementById('final-komi').textContent = score.komi;
            document.getElementById('final-black-total').textContent = score.blackTotal;
            document.getElementById('final-white-total').textContent = score.whiteTotal;
        }
        
        modal.classList.remove('hidden');
    }

    /**
     * 隱藏遊戲結束對話框
     */
    hideGameEndModal() {
        const modal = document.getElementById('game-end-modal');
        modal.classList.add('hidden');
    }

    /**
     * 取得難度文字
     * @param {string} difficulty - 難度代碼，如果不提供則使用 this.aiDifficulty
     * @returns {string} 難度文字
     */
    getDifficultyText(difficulty = this.aiDifficulty) {
        const difficultyMap = {
            'easy': '初級',
            'medium': '中級',
            'hard': '高級',
            'expert': '專家'
        };
        return difficultyMap[difficulty] || difficulty;
    }
}

// 初始化遊戲控制器
const gameController = new GameController();

// 將實例暴露到全域範圍 (用於除錯)
window.gameController = gameController;
