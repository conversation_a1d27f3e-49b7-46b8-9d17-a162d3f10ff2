/**
 * 圍棋遊戲主程式
 * 處理選單導航和遊戲初始化
 */

class GoGameMain {
    /**
     * 建構函式
     */
    constructor() {
        this.selectedMode = null; // 'single', 'two-player', 'ai-vs-ai'
        this.selectedSize = null; // 13 或 19
        this.selectedDifficulty = null; // 'easy', 'medium', 'hard', 'expert'
        this.blackAiDifficulty = null; // AI vs AI 模式的黑子 AI 難度
        this.whiteAiDifficulty = null; // AI vs AI 模式的白子 AI 難度

        this.init();
    }

    /**
     * 初始化遊戲
     */
    init() {
        // 等待 DOM 載入完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupEventListeners();
            });
        } else {
            this.setupEventListeners();
        }
    }

    /**
     * 設置事件監聽器
     */
    setupEventListeners() {
        // 模式選擇按鈕
        const singlePlayerBtn = document.getElementById('single-player-btn');
        const twoPlayerBtn = document.getElementById('two-player-btn');
        
        if (singlePlayerBtn) {
            singlePlayerBtn.addEventListener('click', () => {
                this.selectMode('single');
            });
        }
        
        if (twoPlayerBtn) {
            twoPlayerBtn.addEventListener('click', () => {
                this.selectMode('two-player');
            });
        }

        // AI vs AI 模式按鈕
        const aiVsAiBtn = document.getElementById('ai-vs-ai-btn');
        if (aiVsAiBtn) {
            aiVsAiBtn.addEventListener('click', () => {
                this.selectMode('ai-vs-ai');
            });
        }

        // 棋盤大小選擇按鈕
        const size13Btn = document.getElementById('size-13-btn');
        const size19Btn = document.getElementById('size-19-btn');
        
        if (size13Btn) {
            size13Btn.addEventListener('click', () => {
                this.selectBoardSize(13);
            });
        }
        
        if (size19Btn) {
            size19Btn.addEventListener('click', () => {
                this.selectBoardSize(19);
            });
        }

        // AI 難度選擇按鈕
        const easyBtn = document.getElementById('easy-btn');
        const mediumBtn = document.getElementById('medium-btn');
        const hardBtn = document.getElementById('hard-btn');
        const expertBtn = document.getElementById('expert-btn');

        if (easyBtn) {
            easyBtn.addEventListener('click', () => {
                this.selectDifficulty('easy');
            });
        }

        if (mediumBtn) {
            mediumBtn.addEventListener('click', () => {
                this.selectDifficulty('medium');
            });
        }

        if (hardBtn) {
            hardBtn.addEventListener('click', () => {
                this.selectDifficulty('hard');
            });
        }

        if (expertBtn) {
            expertBtn.addEventListener('click', () => {
                this.selectDifficulty('expert');
            });
        }

        // 返回按鈕
        const backToModeBtn = document.getElementById('back-to-mode-btn');
        const backToSizeBtn = document.getElementById('back-to-size-btn');
        const backToSizeAiBtn = document.getElementById('back-to-size-ai-btn');

        if (backToModeBtn) {
            backToModeBtn.addEventListener('click', () => {
                this.showModeSelection();
            });
        }

        if (backToSizeBtn) {
            backToSizeBtn.addEventListener('click', () => {
                this.showBoardSizeSelection();
            });
        }

        if (backToSizeAiBtn) {
            backToSizeAiBtn.addEventListener('click', () => {
                this.showBoardSizeSelection();
            });
        }

        // AI vs AI 難度選擇
        this.setupAiVsAiSelection();

        // AI vs AI 開始按鈕
        const startAiBattleBtn = document.getElementById('start-ai-battle-btn');
        if (startAiBattleBtn) {
            startAiBattleBtn.addEventListener('click', () => {
                this.startGame();
            });
        }

        console.log('圍棋遊戲選單初始化完成');
    }

    /**
     * 設置 AI vs AI 選擇邏輯
     */
    setupAiVsAiSelection() {
        // 黑子 AI 難度選擇
        const blackAiButtons = document.querySelectorAll('[data-player="black"]');
        blackAiButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                // 移除其他按鈕的選中狀態
                blackAiButtons.forEach(b => b.classList.remove('selected'));
                // 設置當前按鈕為選中
                btn.classList.add('selected');

                this.blackAiDifficulty = btn.dataset.difficulty;
                document.getElementById('black-ai-selected').textContent =
                    this.getDifficultyText(this.blackAiDifficulty);

                this.updateStartBattleButton();
            });
        });

        // 白子 AI 難度選擇
        const whiteAiButtons = document.querySelectorAll('[data-player="white"]');
        whiteAiButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                // 移除其他按鈕的選中狀態
                whiteAiButtons.forEach(b => b.classList.remove('selected'));
                // 設置當前按鈕為選中
                btn.classList.add('selected');

                this.whiteAiDifficulty = btn.dataset.difficulty;
                document.getElementById('white-ai-selected').textContent =
                    this.getDifficultyText(this.whiteAiDifficulty);

                this.updateStartBattleButton();
            });
        });
    }

    /**
     * 更新開始對戰按鈕狀態
     */
    updateStartBattleButton() {
        const startBtn = document.getElementById('start-ai-battle-btn');
        if (startBtn) {
            const canStart = this.blackAiDifficulty && this.whiteAiDifficulty;
            startBtn.disabled = !canStart;

            if (canStart) {
                startBtn.textContent = `開始對戰：${this.getDifficultyText(this.blackAiDifficulty)} vs ${this.getDifficultyText(this.whiteAiDifficulty)}`;
            } else {
                startBtn.textContent = '請選擇雙方 AI 難度';
            }
        }
    }

    /**
     * 選擇遊戲模式
     * @param {string} mode - 遊戲模式
     */
    selectMode(mode) {
        this.selectedMode = mode;
        console.log(`選擇遊戲模式: ${mode}`);
        
        this.showBoardSizeSelection();
    }

    /**
     * 選擇棋盤大小
     * @param {number} size - 棋盤大小
     */
    selectBoardSize(size) {
        this.selectedSize = size;
        console.log(`選擇棋盤大小: ${size}路`);

        if (this.selectedMode === 'single') {
            // 單機版需要選擇 AI 難度
            this.showAIDifficultySelection();
        } else if (this.selectedMode === 'ai-vs-ai') {
            // AI vs AI 需要選擇雙方難度
            this.showAiVsAiSelection();
        } else {
            // 雙人對戰直接開始遊戲
            this.startGame();
        }
    }

    /**
     * 選擇 AI 難度
     * @param {string} difficulty - AI 難度
     */
    selectDifficulty(difficulty) {
        this.selectedDifficulty = difficulty;
        console.log(`選擇 AI 難度: ${difficulty}`);
        
        this.startGame();
    }

    /**
     * 顯示模式選擇
     */
    showModeSelection() {
        this.hideAllSections();
        document.querySelector('.mode-selection').classList.remove('hidden');
        document.querySelector('.game-rules').classList.remove('hidden');
    }

    /**
     * 顯示棋盤大小選擇
     */
    showBoardSizeSelection() {
        this.hideAllSections();
        document.getElementById('board-size-selection').classList.remove('hidden');
    }

    /**
     * 顯示 AI 難度選擇
     */
    showAIDifficultySelection() {
        this.hideAllSections();
        document.getElementById('ai-difficulty-selection').classList.remove('hidden');
    }

    /**
     * 顯示 AI vs AI 選擇
     */
    showAiVsAiSelection() {
        this.hideAllSections();
        document.getElementById('ai-vs-ai-selection').classList.remove('hidden');
    }

    /**
     * 隱藏所有選擇區域
     */
    hideAllSections() {
        const sections = [
            '.mode-selection',
            '#board-size-selection',
            '#ai-difficulty-selection',
            '#ai-vs-ai-selection',
            '.game-rules'
        ];

        sections.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                element.classList.add('hidden');
            }
        });
    }

    /**
     * 開始遊戲
     */
    startGame() {
        console.log('開始遊戲！');
        console.log(`模式: ${this.selectedMode}`);
        console.log(`棋盤: ${this.selectedSize}路`);

        if (this.selectedMode === 'single') {
            console.log(`AI 難度: ${this.selectedDifficulty}`);
        } else if (this.selectedMode === 'ai-vs-ai') {
            console.log(`黑子 AI: ${this.blackAiDifficulty}`);
            console.log(`白子 AI: ${this.whiteAiDifficulty}`);
        }

        // 顯示載入動畫
        this.showLoadingAnimation();

        // 延遲跳轉到遊戲頁面
        setTimeout(() => {
            this.navigateToGame();
        }, 1500);
    }

    /**
     * 跳轉到遊戲頁面
     */
    navigateToGame() {
        const params = new URLSearchParams({
            mode: this.selectedMode,
            size: this.selectedSize
        });

        if (this.selectedDifficulty) {
            params.set('difficulty', this.selectedDifficulty);
        }

        if (this.selectedMode === 'ai-vs-ai') {
            params.set('blackAi', this.blackAiDifficulty);
            params.set('whiteAi', this.whiteAiDifficulty);
        }

        window.location.href = `game.html?${params.toString()}`;
    }

    /**
     * 顯示載入動畫
     */
    showLoadingAnimation() {
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'loading-overlay';
        loadingDiv.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <p>正在準備棋盤...</p>
                <div class="loading-details">
                    <span>模式: ${this.getModeText()}</span>
                    <span>棋盤: ${this.selectedSize}路</span>
                    ${this.getAiInfoText()}
                </div>
            </div>
        `;
        
        // 添加載入樣式
        loadingDiv.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(44, 24, 16, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            animation: fadeIn 0.3s ease-out;
        `;
        
        const style = document.createElement('style');
        style.textContent = `
            .loading-content {
                text-align: center;
                color: #f5f5dc;
                font-size: 1.2rem;
            }
            .loading-spinner {
                width: 60px;
                height: 60px;
                border: 4px solid rgba(245, 245, 220, 0.3);
                border-top: 4px solid #f5f5dc;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 20px;
            }
            .loading-details {
                display: flex;
                flex-direction: column;
                gap: 8px;
                margin-top: 15px;
                font-size: 1rem;
                opacity: 0.8;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(loadingDiv);
    }

    /**
     * 取得模式文字
     * @returns {string} 模式文字
     */
    getModeText() {
        const modeMap = {
            'single': '單機版',
            'two-player': '雙人對戰',
            'ai-vs-ai': 'AI 對戰'
        };
        return modeMap[this.selectedMode] || this.selectedMode;
    }

    /**
     * 取得 AI 資訊文字
     * @returns {string} AI 資訊文字
     */
    getAiInfoText() {
        if (this.selectedMode === 'single' && this.selectedDifficulty) {
            return `<span>AI 難度: ${this.getDifficultyText(this.selectedDifficulty)}</span>`;
        } else if (this.selectedMode === 'ai-vs-ai') {
            return `<span>黑子: ${this.getDifficultyText(this.blackAiDifficulty)} | 白子: ${this.getDifficultyText(this.whiteAiDifficulty)}</span>`;
        }
        return '';
    }

    /**
     * 取得難度文字
     * @param {string} difficulty - 難度代碼
     * @returns {string} 難度文字
     */
    getDifficultyText(difficulty) {
        const difficultyMap = {
            'easy': '初級',
            'medium': '中級',
            'hard': '高級',
            'expert': '專家'
        };
        return difficultyMap[difficulty] || difficulty;
    }
}

// 初始化遊戲
const goGameMain = new GoGameMain();

// 將實例暴露到全域範圍 (用於除錯)
window.goGameMain = goGameMain;
