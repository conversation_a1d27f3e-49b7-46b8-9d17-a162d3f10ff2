/**
 * 圍棋 AI 類別
 * 實現不同難度的 AI 對手
 */

class GoAI {
    /**
     * 建構函式
     * @param {string} difficulty - AI 難度 ('easy', 'medium', 'hard')
     * @param {number} color - AI 執子顏色 (1=黑, 2=白)
     */
    constructor(difficulty = 'medium', color = 2) {
        this.difficulty = difficulty;
        this.color = color;
        this.opponentColor = color === 1 ? 2 : 1;
        this.thinkingTime = this.getThinkingTime();
    }

    /**
     * 取得思考時間
     * @returns {number} 思考時間 (毫秒)
     */
    getThinkingTime() {
        const times = {
            easy: 500,
            medium: 1000,
            hard: 2000
        };
        return times[this.difficulty] || 1000;
    }

    /**
     * 選擇下一步
     * @param {GoGame} game - 遊戲實例
     * @returns {Promise} 下棋位置或 pass
     */
    async chooseMove(game) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const move = this.calculateBestMove(game);
                resolve(move);
            }, this.thinkingTime);
        });
    }

    /**
     * 計算最佳下法
     * @param {GoGame} game - 遊戲實例
     * @returns {Object} 下棋結果
     */
    calculateBestMove(game) {
        const validMoves = this.getValidMoves(game);
        
        if (validMoves.length === 0) {
            return { pass: true };
        }

        let bestMove;
        
        switch (this.difficulty) {
            case 'easy':
                bestMove = this.chooseRandomMove(validMoves);
                break;
            case 'medium':
                bestMove = this.chooseStrategicMove(game, validMoves);
                break;
            case 'hard':
                bestMove = this.chooseAdvancedMove(game, validMoves);
                break;
            default:
                bestMove = this.chooseRandomMove(validMoves);
        }

        return bestMove;
    }

    /**
     * 取得所有有效下法
     * @param {GoGame} game - 遊戲實例
     * @returns {Array} 有效位置陣列
     */
    getValidMoves(game) {
        const validMoves = [];
        
        for (let row = 0; row < game.size; row++) {
            for (let col = 0; col < game.size; col++) {
                if (game.board[row][col] === 0) {
                    // 檢查是否為有效下法
                    if (!game.isSuicideMove(row, col, this.color) && 
                        !game.isKoViolation(row, col)) {
                        validMoves.push([row, col]);
                    }
                }
            }
        }
        
        return validMoves;
    }

    /**
     * 隨機選擇下法 (簡單難度)
     * @param {Array} validMoves - 有效下法陣列
     * @returns {Object} 選擇的下法
     */
    chooseRandomMove(validMoves) {
        const randomIndex = Math.floor(Math.random() * validMoves.length);
        const [row, col] = validMoves[randomIndex];
        return { row, col };
    }

    /**
     * 策略性選擇下法 (中等難度)
     * @param {GoGame} game - 遊戲實例
     * @param {Array} validMoves - 有效下法陣列
     * @returns {Object} 選擇的下法
     */
    chooseStrategicMove(game, validMoves) {
        // 評估每個位置的分數
        const scoredMoves = validMoves.map(([row, col]) => {
            const score = this.evaluatePosition(game, row, col);
            return { row, col, score };
        });

        // 排序並選擇最佳位置
        scoredMoves.sort((a, b) => b.score - a.score);
        
        // 添加一些隨機性，從前幾個最佳選擇中隨機選一個
        const topMoves = scoredMoves.slice(0, Math.min(5, scoredMoves.length));
        const selectedMove = topMoves[Math.floor(Math.random() * topMoves.length)];
        
        return { row: selectedMove.row, col: selectedMove.col };
    }

    /**
     * 高級選擇下法 (困難難度)
     * @param {GoGame} game - 遊戲實例
     * @param {Array} validMoves - 有效下法陣列
     * @returns {Object} 選擇的下法
     */
    chooseAdvancedMove(game, validMoves) {
        // 使用更深入的評估
        const scoredMoves = validMoves.map(([row, col]) => {
            const score = this.evaluatePositionAdvanced(game, row, col);
            return { row, col, score };
        });

        // 排序並選擇最佳位置
        scoredMoves.sort((a, b) => b.score - a.score);
        
        // 困難模式選擇最佳位置，但仍保留少量隨機性
        const topMoves = scoredMoves.slice(0, Math.min(3, scoredMoves.length));
        const selectedMove = topMoves[Math.floor(Math.random() * topMoves.length)];
        
        return { row: selectedMove.row, col: selectedMove.col };
    }

    /**
     * 評估位置分數 (基礎版本)
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 位置分數
     */
    evaluatePosition(game, row, col) {
        let score = 0;
        
        // 中心位置加分 (圍棋通常從四周開始下)
        const centerRow = Math.floor(game.size / 2);
        const centerCol = Math.floor(game.size / 2);
        const distanceFromCenter = Math.abs(row - centerRow) + Math.abs(col - centerCol);
        score += (game.size - distanceFromCenter) * 2;
        
        // 靠近對手棋子加分
        const adjacentPositions = game.getAdjacentPositions(row, col);
        adjacentPositions.forEach(([r, c]) => {
            if (game.board[r][c] === this.opponentColor) {
                score += 10;
            } else if (game.board[r][c] === this.color) {
                score += 5;
            }
        });
        
        // 檢查是否能吃子
        const captureScore = this.evaluateCapture(game, row, col);
        score += captureScore * 20;
        
        // 檢查是否能救自己的棋子
        const rescueScore = this.evaluateRescue(game, row, col);
        score += rescueScore * 15;
        
        return score;
    }

    /**
     * 評估位置分數 (高級版本)
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 位置分數
     */
    evaluatePositionAdvanced(game, row, col) {
        let score = this.evaluatePosition(game, row, col);
        
        // 形狀評估
        score += this.evaluateShape(game, row, col) * 5;
        
        // 領域評估
        score += this.evaluateTerritory(game, row, col) * 3;
        
        // 眼位評估
        score += this.evaluateEye(game, row, col) * 8;
        
        return score;
    }

    /**
     * 評估吃子機會
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 吃子分數
     */
    evaluateCapture(game, row, col) {
        // 暫時放置棋子
        game.board[row][col] = this.color;
        
        let captureCount = 0;
        const adjacentPositions = game.getAdjacentPositions(row, col);
        
        adjacentPositions.forEach(([r, c]) => {
            if (game.board[r][c] === this.opponentColor) {
                const group = game.getGroup(r, c, this.opponentColor);
                if (game.getLiberties(group) === 0) {
                    captureCount += group.length;
                }
            }
        });
        
        // 移除暫時放置的棋子
        game.board[row][col] = 0;
        
        return captureCount;
    }

    /**
     * 評估救子機會
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 救子分數
     */
    evaluateRescue(game, row, col) {
        // 暫時放置棋子
        game.board[row][col] = this.color;
        
        let rescueScore = 0;
        const adjacentPositions = game.getAdjacentPositions(row, col);
        
        adjacentPositions.forEach(([r, c]) => {
            if (game.board[r][c] === this.color) {
                const group = game.getGroup(r, c, this.color);
                const liberties = game.getLiberties(group);
                
                // 如果這步棋能增加己方棋子的氣
                if (liberties > 0) {
                    rescueScore += group.length / liberties;
                }
            }
        });
        
        // 移除暫時放置的棋子
        game.board[row][col] = 0;
        
        return rescueScore;
    }

    /**
     * 評估形狀
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 形狀分數
     */
    evaluateShape(game, row, col) {
        // 簡單的形狀評估：避免愚形，鼓勵好形
        let shapeScore = 0;
        
        // 檢查是否形成連接
        const adjacentFriendly = game.getAdjacentPositions(row, col)
            .filter(([r, c]) => game.board[r][c] === this.color).length;
        
        shapeScore += adjacentFriendly * 2;
        
        return shapeScore;
    }

    /**
     * 評估領域
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 領域分數
     */
    evaluateTerritory(game, row, col) {
        // 簡單的領域評估
        return Math.random() * 5; // 暫時使用隨機值
    }

    /**
     * 評估眼位
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 眼位分數
     */
    evaluateEye(game, row, col) {
        // 簡單的眼位評估
        const surroundingFriendly = game.getAdjacentPositions(row, col)
            .filter(([r, c]) => game.board[r][c] === this.color).length;
        
        return surroundingFriendly >= 3 ? 10 : 0;
    }
}
