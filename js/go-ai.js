/**
 * 圍棋 AI 類別
 * 實現不同難度的 AI 對手，包含戰略思考和深度搜尋
 */

class GoAI {
    /**
     * 建構函式
     * @param {string} difficulty - AI 難度 ('easy', 'medium', 'hard', 'expert')
     * @param {number} color - AI 執子顏色 (1=黑, 2=白)
     */
    constructor(difficulty = 'medium', color = 2, boardSize = 19) {
        this.difficulty = difficulty;
        this.color = color;
        this.opponentColor = color === 1 ? 2 : 1;
        this.boardSize = boardSize;
        this.thinkingTime = this.getThinkingTime();
        this.searchDepth = this.getSearchDepth();
        this.moveHistory = [];
        this.openingBook = this.initializeOpeningBook();
        this.positionCache = new Map();
    }

    /**
     * 取得思考時間
     * @returns {number} 思考時間 (毫秒)
     */
    getThinkingTime() {
        const times = {
            easy: 500,
            medium: 1000,
            hard: 2000,
            expert: 3000
        };
        return times[this.difficulty] || 1000;
    }

    /**
     * 取得搜尋深度
     * @returns {number} 搜尋深度
     */
    getSearchDepth() {
        const depths = {
            easy: 1,
            medium: 2,
            hard: 3,
            expert: 4
        };
        return depths[this.difficulty] || 2;
    }

    /**
     * 初始化開局庫
     * @returns {Array} 開局定石陣列
     */
    initializeOpeningBook() {
        // 根據棋盤大小動態生成開局位置
        const openings = [];

        if (this.boardSize >= 19) {
            // 19路棋盤的標準開局
            openings.push(
                // 星位
                [3, 3], [3, 15], [15, 3], [15, 15],
                // 小目
                [3, 4], [4, 3], [3, 14], [4, 15], [15, 4], [14, 3], [15, 14], [14, 15],
                // 高目
                [3, 5], [5, 3], [3, 13], [5, 15], [15, 5], [13, 3], [15, 13], [13, 15]
            );
        } else if (this.boardSize >= 13) {
            // 13路棋盤的開局
            openings.push(
                // 星位
                [3, 3], [3, 9], [9, 3], [9, 9],
                // 小目
                [3, 4], [4, 3], [3, 8], [4, 9], [9, 4], [8, 3], [9, 8], [8, 9]
            );
        } else {
            // 9路棋盤的開局
            openings.push(
                [2, 2], [2, 6], [6, 2], [6, 6],
                [2, 3], [3, 2], [2, 5], [3, 6], [6, 3], [5, 2], [6, 5], [5, 6]
            );
        }

        return openings;
    }

    /**
     * 選擇下一步
     * @param {GoGame} game - 遊戲實例
     * @returns {Promise} 下棋位置或 pass
     */
    async chooseMove(game) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const move = this.calculateBestMove(game);
                resolve(move);
            }, this.thinkingTime);
        });
    }

    /**
     * 計算最佳下法
     * @param {GoGame} game - 遊戲實例
     * @returns {Object} 下棋結果
     */
    calculateBestMove(game) {
        // 記錄當前局面
        this.moveHistory.push(this.getBoardHash(game));

        // 檢查開局庫
        if (game.moveCount < 10) {
            const openingMove = this.getOpeningMove(game);
            if (openingMove) {
                return openingMove;
            }
        }

        const validMoves = this.getValidMoves(game);

        if (validMoves.length === 0) {
            return { pass: true };
        }

        let bestMove;

        switch (this.difficulty) {
            case 'easy':
                bestMove = this.chooseRandomMove(validMoves);
                break;
            case 'medium':
                bestMove = this.chooseStrategicMove(game, validMoves);
                break;
            case 'hard':
                bestMove = this.chooseAdvancedMove(game, validMoves);
                break;
            case 'expert':
                bestMove = this.chooseExpertMove(game, validMoves);
                break;
            default:
                bestMove = this.chooseRandomMove(validMoves);
        }

        return bestMove;
    }

    /**
     * 取得所有有效下法
     * @param {GoGame} game - 遊戲實例
     * @returns {Array} 有效位置陣列
     */
    getValidMoves(game) {
        const validMoves = [];

        for (let row = 0; row < game.size; row++) {
            for (let col = 0; col < game.size; col++) {
                if (game.board[row][col] === 0) {
                    // 檢查是否為有效下法
                    if (!game.isSuicideMove(row, col, this.color) &&
                        !game.isKoViolation(row, col)) {
                        validMoves.push([row, col]);
                    }
                }
            }
        }

        return validMoves;
    }

    /**
     * 取得棋盤雜湊值
     * @param {GoGame} game - 遊戲實例
     * @returns {string} 棋盤雜湊值
     */
    getBoardHash(game) {
        return game.board.map(row => row.join('')).join('');
    }

    /**
     * 從開局庫選擇下法
     * @param {GoGame} game - 遊戲實例
     * @returns {Object|null} 開局下法或 null
     */
    getOpeningMove(game) {
        const availableOpenings = this.openingBook.filter(([row, col]) => {
            return row < game.size && col < game.size && game.board[row][col] === 0;
        });

        if (availableOpenings.length > 0) {
            const randomIndex = Math.floor(Math.random() * availableOpenings.length);
            const [row, col] = availableOpenings[randomIndex];
            return { row, col };
        }

        return null;
    }

    /**
     * 隨機選擇下法 (簡單難度)
     * @param {Array} validMoves - 有效下法陣列
     * @returns {Object} 選擇的下法
     */
    chooseRandomMove(validMoves) {
        const randomIndex = Math.floor(Math.random() * validMoves.length);
        const [row, col] = validMoves[randomIndex];
        return { row, col };
    }

    /**
     * 策略性選擇下法 (中等難度)
     * @param {GoGame} game - 遊戲實例
     * @param {Array} validMoves - 有效下法陣列
     * @returns {Object} 選擇的下法
     */
    chooseStrategicMove(game, validMoves) {
        // 評估每個位置的分數
        const scoredMoves = validMoves.map(([row, col]) => {
            const score = this.evaluatePosition(game, row, col);
            return { row, col, score };
        });

        // 排序並選擇最佳位置
        scoredMoves.sort((a, b) => b.score - a.score);
        
        // 添加一些隨機性，從前幾個最佳選擇中隨機選一個
        const topMoves = scoredMoves.slice(0, Math.min(5, scoredMoves.length));
        const selectedMove = topMoves[Math.floor(Math.random() * topMoves.length)];
        
        return { row: selectedMove.row, col: selectedMove.col };
    }

    /**
     * 高級選擇下法 (困難難度)
     * @param {GoGame} game - 遊戲實例
     * @param {Array} validMoves - 有效下法陣列
     * @returns {Object} 選擇的下法
     */
    chooseAdvancedMove(game, validMoves) {
        // 使用更深入的評估
        const scoredMoves = validMoves.map(([row, col]) => {
            const score = this.evaluatePositionAdvanced(game, row, col);
            return { row, col, score };
        });

        // 排序並選擇最佳位置
        scoredMoves.sort((a, b) => b.score - a.score);

        // 困難模式選擇最佳位置，但仍保留少量隨機性
        const topMoves = scoredMoves.slice(0, Math.min(3, scoredMoves.length));
        const selectedMove = topMoves[Math.floor(Math.random() * topMoves.length)];

        return { row: selectedMove.row, col: selectedMove.col };
    }

    /**
     * 專家級選擇下法 (專家難度)
     * @param {GoGame} game - 遊戲實例
     * @param {Array} validMoves - 有效下法陣列
     * @returns {Object} 選擇的下法
     */
    chooseExpertMove(game, validMoves) {
        // 使用 minimax 搜尋
        const bestMove = this.minimax(game, this.searchDepth, -Infinity, Infinity, true);

        if (bestMove && bestMove.row !== undefined && bestMove.col !== undefined) {
            return { row: bestMove.row, col: bestMove.col };
        }

        // 如果 minimax 失敗，回退到高級評估
        return this.chooseAdvancedMove(game, validMoves);
    }

    /**
     * Minimax 搜尋演算法
     * @param {GoGame} game - 遊戲實例
     * @param {number} depth - 搜尋深度
     * @param {number} alpha - Alpha 值
     * @param {number} beta - Beta 值
     * @param {boolean} maximizingPlayer - 是否為最大化玩家
     * @returns {Object} 最佳下法和分數
     */
    minimax(game, depth, alpha, beta, maximizingPlayer) {
        if (depth === 0) {
            return { score: this.evaluateBoardPosition(game) };
        }

        const validMoves = this.getValidMoves(game);
        if (validMoves.length === 0) {
            return { score: this.evaluateBoardPosition(game) };
        }

        let bestMove = null;

        if (maximizingPlayer) {
            let maxEval = -Infinity;

            for (const [row, col] of validMoves) {
                // 模擬下棋
                game.board[row][col] = this.color;
                const evaluation = this.minimax(game, depth - 1, alpha, beta, false);
                game.board[row][col] = 0; // 撤銷

                if (evaluation.score > maxEval) {
                    maxEval = evaluation.score;
                    bestMove = { row, col, score: maxEval };
                }

                alpha = Math.max(alpha, evaluation.score);
                if (beta <= alpha) {
                    break; // Alpha-beta 剪枝
                }
            }

            return bestMove || { score: maxEval };
        } else {
            let minEval = Infinity;

            for (const [row, col] of validMoves) {
                // 模擬對手下棋
                game.board[row][col] = this.opponentColor;
                const evaluation = this.minimax(game, depth - 1, alpha, beta, true);
                game.board[row][col] = 0; // 撤銷

                if (evaluation.score < minEval) {
                    minEval = evaluation.score;
                    bestMove = { row, col, score: minEval };
                }

                beta = Math.min(beta, evaluation.score);
                if (beta <= alpha) {
                    break; // Alpha-beta 剪枝
                }
            }

            return bestMove || { score: minEval };
        }
    }

    /**
     * 評估整個棋盤局面
     * @param {GoGame} game - 遊戲實例
     * @returns {number} 棋盤評估分數
     */
    evaluateBoardPosition(game) {
        let score = 0;

        // 計算雙方棋子數量
        let myStones = 0;
        let opponentStones = 0;

        for (let row = 0; row < game.size; row++) {
            for (let col = 0; col < game.size; col++) {
                if (game.board[row][col] === this.color) {
                    myStones++;
                } else if (game.board[row][col] === this.opponentColor) {
                    opponentStones++;
                }
            }
        }

        // 基本分數差
        score += (myStones - opponentStones) * 10;

        // 評估領域控制
        score += this.evaluateTerritoryControl(game);

        // 評估棋子活力 (氣的數量)
        score += this.evaluateStoneLiberty(game);

        return score;
    }

    /**
     * 評估位置分數 (基礎版本)
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 位置分數
     */
    evaluatePosition(game, row, col) {
        let score = 0;

        // 圍棋位置價值評估：角落 > 邊線 > 中央
        score += this.evaluatePositionValue(game, row, col);

        // 靠近對手棋子加分（接觸戰）
        const adjacentPositions = game.getAdjacentPositions(row, col);
        adjacentPositions.forEach(([r, c]) => {
            if (game.board[r][c] === this.opponentColor) {
                score += 10;
            } else if (game.board[r][c] === this.color) {
                score += 5;
            }
        });

        // 檢查是否能吃子（最高優先級）
        const captureScore = this.evaluateCapture(game, row, col);
        score += captureScore * 50;

        // 檢查是否能救自己的棋子（高優先級）
        const rescueScore = this.evaluateRescue(game, row, col);
        score += rescueScore * 30;

        // 避免過於靠近邊緣（除非是開局）
        if (game.moveCount > 20) {
            const edgeDistance = Math.min(row, col, game.size - 1 - row, game.size - 1 - col);
            if (edgeDistance === 0) {
                score -= 5; // 邊線減分
            }
        }

        return score;
    }

    /**
     * 評估位置的基本價值
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 位置價值分數
     */
    evaluatePositionValue(game, row, col) {
        const size = game.size;
        let score = 0;

        // 計算到邊緣的距離
        const edgeDistance = Math.min(row, col, size - 1 - row, size - 1 - col);

        // 角落位置（最有價值）
        if ((row <= 3 || row >= size - 4) && (col <= 3 || col >= size - 4)) {
            score += 15;
        }
        // 邊線位置
        else if (edgeDistance <= 2) {
            score += 8;
        }
        // 中央位置（開局階段價值較低）
        else {
            if (game.moveCount < 30) {
                score += 2; // 開局中央價值低
            } else {
                score += 6; // 中盤中央價值提升
            }
        }

        return score;
    }

    /**
     * 評估位置分數 (高級版本)
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 位置分數
     */
    evaluatePositionAdvanced(game, row, col) {
        let score = this.evaluatePosition(game, row, col);
        
        // 形狀評估
        score += this.evaluateShape(game, row, col) * 5;
        
        // 領域評估
        score += this.evaluateTerritory(game, row, col) * 3;
        
        // 眼位評估
        score += this.evaluateEye(game, row, col) * 8;
        
        return score;
    }

    /**
     * 評估吃子機會
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 吃子分數
     */
    evaluateCapture(game, row, col) {
        // 暫時放置棋子
        game.board[row][col] = this.color;
        
        let captureCount = 0;
        const adjacentPositions = game.getAdjacentPositions(row, col);
        
        adjacentPositions.forEach(([r, c]) => {
            if (game.board[r][c] === this.opponentColor) {
                const group = game.getGroup(r, c, this.opponentColor);
                if (game.getLiberties(group) === 0) {
                    captureCount += group.length;
                }
            }
        });
        
        // 移除暫時放置的棋子
        game.board[row][col] = 0;
        
        return captureCount;
    }

    /**
     * 評估救子機會
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 救子分數
     */
    evaluateRescue(game, row, col) {
        // 暫時放置棋子
        game.board[row][col] = this.color;
        
        let rescueScore = 0;
        const adjacentPositions = game.getAdjacentPositions(row, col);
        
        adjacentPositions.forEach(([r, c]) => {
            if (game.board[r][c] === this.color) {
                const group = game.getGroup(r, c, this.color);
                const liberties = game.getLiberties(group);
                
                // 如果這步棋能增加己方棋子的氣
                if (liberties > 0) {
                    rescueScore += group.length / liberties;
                }
            }
        });
        
        // 移除暫時放置的棋子
        game.board[row][col] = 0;
        
        return rescueScore;
    }

    /**
     * 評估形狀
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 形狀分數
     */
    evaluateShape(game, row, col) {
        // 簡單的形狀評估：避免愚形，鼓勵好形
        let shapeScore = 0;
        
        // 檢查是否形成連接
        const adjacentFriendly = game.getAdjacentPositions(row, col)
            .filter(([r, c]) => game.board[r][c] === this.color).length;
        
        shapeScore += adjacentFriendly * 2;
        
        return shapeScore;
    }

    /**
     * 評估領域控制
     * @param {GoGame} game - 遊戲實例
     * @returns {number} 領域控制分數
     */
    evaluateTerritoryControl(game) {
        let score = 0;
        const visited = Array(game.size).fill().map(() => Array(game.size).fill(false));

        for (let row = 0; row < game.size; row++) {
            for (let col = 0; col < game.size; col++) {
                if (game.board[row][col] === 0 && !visited[row][col]) {
                    const territory = this.getTerritory(game, row, col, visited);
                    if (territory.owner === this.color) {
                        score += territory.size * 2;
                    } else if (territory.owner === this.opponentColor) {
                        score -= territory.size * 2;
                    }
                }
            }
        }

        return score;
    }

    /**
     * 評估棋子活力
     * @param {GoGame} game - 遊戲實例
     * @returns {number} 活力分數
     */
    evaluateStoneLiberty(game) {
        let score = 0;
        const visited = Array(game.size).fill().map(() => Array(game.size).fill(false));

        for (let row = 0; row < game.size; row++) {
            for (let col = 0; col < game.size; col++) {
                if (!visited[row][col] && game.board[row][col] !== 0) {
                    const group = game.getGroup(row, col, game.board[row][col]);
                    const liberties = game.getLiberties(group);

                    // 標記群組為已訪問
                    group.forEach(([r, c]) => {
                        visited[r][c] = true;
                    });

                    if (game.board[row][col] === this.color) {
                        score += liberties * group.length;
                    } else {
                        score -= liberties * group.length;
                    }
                }
            }
        }

        return score;
    }

    /**
     * 取得領域資訊
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 起始行座標
     * @param {number} col - 起始列座標
     * @param {Array} visited - 已訪問陣列
     * @returns {Object} 領域資訊
     */
    getTerritory(game, row, col, visited) {
        const territory = [];
        const stack = [[row, col]];
        const borders = new Set();

        while (stack.length > 0) {
            const [r, c] = stack.pop();

            if (r < 0 || r >= game.size || c < 0 || c >= game.size || visited[r][c]) {
                continue;
            }

            if (game.board[r][c] !== 0) {
                borders.add(game.board[r][c]);
                continue;
            }

            visited[r][c] = true;
            territory.push([r, c]);

            // 檢查四個方向
            stack.push([r-1, c], [r+1, c], [r, c-1], [r, c+1]);
        }

        // 判斷領域歸屬
        let owner = null;
        if (borders.size === 1) {
            owner = Array.from(borders)[0];
        }

        return {
            size: territory.length,
            owner: owner,
            positions: territory
        };
    }

    /**
     * 評估領域 (單點)
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 領域分數
     */
    evaluateTerritory(game, row, col) {
        let score = 0;
        const radius = 3;

        // 檢查周圍區域的控制情況
        for (let r = Math.max(0, row - radius); r < Math.min(game.size, row + radius + 1); r++) {
            for (let c = Math.max(0, col - radius); c < Math.min(game.size, col + radius + 1); c++) {
                const distance = Math.abs(r - row) + Math.abs(c - col);
                const weight = Math.max(0, radius - distance + 1);

                if (game.board[r][c] === this.color) {
                    score += weight;
                } else if (game.board[r][c] === this.opponentColor) {
                    score -= weight;
                }
            }
        }

        return score;
    }

    /**
     * 評估眼位
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 眼位分數
     */
    evaluateEye(game, row, col) {
        // 簡單的眼位評估
        const surroundingFriendly = game.getAdjacentPositions(row, col)
            .filter(([r, c]) => game.board[r][c] === this.color).length;
        
        return surroundingFriendly >= 3 ? 10 : 0;
    }
}
