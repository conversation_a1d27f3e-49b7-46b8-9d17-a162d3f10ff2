/**
 * 圍棋 AI 類別
 * 實現不同難度的 AI 對手，包含戰略思考和深度搜尋
 */

class GoAI {
    /**
     * 建構函式
     * @param {string} difficulty - AI 難度 ('easy', 'medium', 'hard', 'expert')
     * @param {number} color - AI 執子顏色 (1=黑, 2=白)
     */
    constructor(difficulty = 'medium', color = 2, boardSize = 19) {
        this.difficulty = difficulty;
        this.color = color;
        this.opponentColor = color === 1 ? 2 : 1;
        this.boardSize = boardSize;
        this.thinkingTime = this.getThinkingTime();
        this.searchDepth = this.getSearchDepth();
        this.moveHistory = [];
        this.openingBook = this.initializeOpeningBook();
        this.positionCache = new Map();
    }

    /**
     * 取得思考時間
     * @returns {number} 思考時間 (毫秒)
     */
    getThinkingTime() {
        const times = {
            easy: 500,
            medium: 1000,
            hard: 2000,
            expert: 3000
        };
        return times[this.difficulty] || 1000;
    }

    /**
     * 取得搜尋深度
     * @returns {number} 搜尋深度
     */
    getSearchDepth() {
        const depths = {
            easy: 1,
            medium: 2,
            hard: 3,
            expert: 4
        };
        return depths[this.difficulty] || 2;
    }

    /**
     * 初始化開局庫
     * @returns {Array} 開局定石陣列
     */
    initializeOpeningBook() {
        // 根據棋盤大小動態生成開局位置
        const openings = [];

        if (this.boardSize >= 19) {
            // 19路棋盤的標準開局
            openings.push(
                // 星位
                [3, 3], [3, 15], [15, 3], [15, 15],
                // 小目
                [3, 4], [4, 3], [3, 14], [4, 15], [15, 4], [14, 3], [15, 14], [14, 15],
                // 高目
                [3, 5], [5, 3], [3, 13], [5, 15], [15, 5], [13, 3], [15, 13], [13, 15]
            );
        } else if (this.boardSize >= 13) {
            // 13路棋盤的開局
            openings.push(
                // 星位
                [3, 3], [3, 9], [9, 3], [9, 9],
                // 小目
                [3, 4], [4, 3], [3, 8], [4, 9], [9, 4], [8, 3], [9, 8], [8, 9]
            );
        } else {
            // 9路棋盤的開局
            openings.push(
                [2, 2], [2, 6], [6, 2], [6, 6],
                [2, 3], [3, 2], [2, 5], [3, 6], [6, 3], [5, 2], [6, 5], [5, 6]
            );
        }

        return openings;
    }

    /**
     * 選擇下一步
     * @param {GoGame} game - 遊戲實例
     * @returns {Promise} 下棋位置或 pass
     */
    async chooseMove(game) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const move = this.calculateBestMove(game);
                resolve(move);
            }, this.thinkingTime);
        });
    }

    /**
     * 計算最佳下法
     * @param {GoGame} game - 遊戲實例
     * @returns {Object} 下棋結果
     */
    calculateBestMove(game) {
        // 記錄當前局面
        this.moveHistory.push(this.getBoardHash(game));

        // 檢查開局庫
        if (game.moveCount < 10) {
            const openingMove = this.getOpeningMove(game);
            if (openingMove) {
                return openingMove;
            }
        }

        const validMoves = this.getValidMoves(game);

        if (validMoves.length === 0) {
            return { pass: true };
        }

        let bestMove;

        switch (this.difficulty) {
            case 'easy':
                bestMove = this.chooseRandomMove(validMoves);
                break;
            case 'medium':
                bestMove = this.chooseStrategicMove(game, validMoves);
                break;
            case 'hard':
                bestMove = this.chooseAdvancedMove(game, validMoves);
                break;
            case 'expert':
                bestMove = this.chooseExpertMove(game, validMoves);
                break;
            default:
                bestMove = this.chooseRandomMove(validMoves);
        }

        return bestMove;
    }

    /**
     * 取得所有有效下法
     * @param {GoGame} game - 遊戲實例
     * @returns {Array} 有效位置陣列
     */
    getValidMoves(game) {
        const validMoves = [];

        for (let row = 0; row < game.size; row++) {
            for (let col = 0; col < game.size; col++) {
                if (game.board[row][col] === 0) {
                    // 檢查是否為有效下法
                    if (!game.isSuicideMove(row, col, this.color) &&
                        !game.isKoViolation(row, col)) {
                        validMoves.push([row, col]);
                    }
                }
            }
        }

        return validMoves;
    }

    /**
     * 取得棋盤雜湊值
     * @param {GoGame} game - 遊戲實例
     * @returns {string} 棋盤雜湊值
     */
    getBoardHash(game) {
        return game.board.map(row => row.join('')).join('');
    }

    /**
     * 從開局庫選擇下法
     * @param {GoGame} game - 遊戲實例
     * @returns {Object|null} 開局下法或 null
     */
    getOpeningMove(game) {
        const availableOpenings = this.openingBook.filter(([row, col]) => {
            return row < game.size && col < game.size && game.board[row][col] === 0;
        });

        if (availableOpenings.length > 0) {
            const randomIndex = Math.floor(Math.random() * availableOpenings.length);
            const [row, col] = availableOpenings[randomIndex];
            return { row, col };
        }

        return null;
    }

    /**
     * 隨機選擇下法 (簡單難度)
     * @param {Array} validMoves - 有效下法陣列
     * @returns {Object} 選擇的下法
     */
    chooseRandomMove(validMoves) {
        const randomIndex = Math.floor(Math.random() * validMoves.length);
        const [row, col] = validMoves[randomIndex];
        return { row, col };
    }

    /**
     * 策略性選擇下法 (中等難度)
     * @param {GoGame} game - 遊戲實例
     * @param {Array} validMoves - 有效下法陣列
     * @returns {Object} 選擇的下法
     */
    chooseStrategicMove(game, validMoves) {
        // 評估每個位置的分數
        const scoredMoves = validMoves.map(([row, col]) => {
            const score = this.evaluatePosition(game, row, col);
            return { row, col, score };
        });

        // 排序並選擇最佳位置
        scoredMoves.sort((a, b) => b.score - a.score);
        
        // 添加一些隨機性，從前幾個最佳選擇中隨機選一個
        const topMoves = scoredMoves.slice(0, Math.min(5, scoredMoves.length));
        const selectedMove = topMoves[Math.floor(Math.random() * topMoves.length)];
        
        return { row: selectedMove.row, col: selectedMove.col };
    }

    /**
     * 高級選擇下法 (困難難度) - 極度積極進攻
     * @param {GoGame} game - 遊戲實例
     * @param {Array} validMoves - 有效下法陣列
     * @returns {Object} 選擇的下法
     */
    chooseAdvancedMove(game, validMoves) {
        // 第一優先：檢查是否有立即吃子的機會
        const captureMoves = this.findCaptureMoves(game, validMoves);
        if (captureMoves.length > 0) {
            // 選擇能吃最多子的位置
            captureMoves.sort((a, b) => b.captureCount - a.captureCount);
            return { row: captureMoves[0].row, col: captureMoves[0].col };
        }

        // 第二優先：檢查是否有攻擊機會（減少對手氣數）
        const attackMoves = this.findAttackMoves(game, validMoves);
        if (attackMoves.length > 0) {
            // 選擇攻擊效果最好的位置
            attackMoves.sort((a, b) => b.attackValue - a.attackValue);
            return { row: attackMoves[0].row, col: attackMoves[0].col };
        }

        // 第三優先：避免過遠佈局，只考慮接觸戰和近距離下法
        const contactMoves = this.findContactMoves(game, validMoves);
        if (contactMoves.length > 0) {
            const scoredMoves = contactMoves.map(([row, col]) => {
                const score = this.evaluatePositionAdvanced(game, row, col);
                return { row, col, score };
            });

            scoredMoves.sort((a, b) => b.score - a.score);
            return { row: scoredMoves[0].row, col: scoredMoves[0].col };
        }

        // 最後選擇：使用標準評估但避免過遠位置
        const nearMoves = this.filterNearMoves(game, validMoves);
        const scoredMoves = nearMoves.map(([row, col]) => {
            const score = this.evaluatePositionAdvanced(game, row, col);
            return { row, col, score };
        });

        scoredMoves.sort((a, b) => b.score - a.score);
        return { row: scoredMoves[0].row, col: scoredMoves[0].col };
    }

    /**
     * 專家級選擇下法 (專家難度) - 最積極進攻
     * @param {GoGame} game - 遊戲實例
     * @param {Array} validMoves - 有效下法陣列
     * @returns {Object} 選擇的下法
     */
    chooseExpertMove(game, validMoves) {
        // 第一優先：立即吃子（絕對優先）
        const captureMoves = this.findCaptureMoves(game, validMoves);
        if (captureMoves.length > 0) {
            // 專家級：選擇能吃最多子的位置，不考慮其他因素
            captureMoves.sort((a, b) => b.captureCount - a.captureCount);
            return { row: captureMoves[0].row, col: captureMoves[0].col };
        }

        // 第二優先：致命攻擊（讓對手棋子只剩1氣）
        const killMoves = this.findKillMoves(game, validMoves);
        if (killMoves.length > 0) {
            // 選擇攻擊最大群組的位置
            killMoves.sort((a, b) => b.targetSize - a.targetSize);
            return { row: killMoves[0].row, col: killMoves[0].col };
        }

        // 第三優先：嚴重威脅（讓對手棋子只剩2氣）
        const threatMoves = this.findThreatMoves(game, validMoves);
        if (threatMoves.length > 0) {
            threatMoves.sort((a, b) => b.threatValue - a.threatValue);
            return { row: threatMoves[0].row, col: threatMoves[0].col };
        }

        // 第四優先：使用 minimax 但限制在接觸戰範圍
        const contactMoves = this.findContactMoves(game, validMoves);
        if (contactMoves.length > 0 && contactMoves.length <= 15) {
            const bestMove = this.minimaxLimited(game, contactMoves, this.searchDepth);
            if (bestMove && bestMove.row !== undefined && bestMove.col !== undefined) {
                return { row: bestMove.row, col: bestMove.col };
            }
        }

        // 最後選擇：避免過遠佈局，專注於戰鬥
        return this.chooseAdvancedMove(game, validMoves);
    }

    /**
     * Minimax 搜尋演算法（改良版，不修改遊戲狀態）
     * @param {GoGame} game - 遊戲實例
     * @param {number} depth - 搜尋深度
     * @param {number} alpha - Alpha 值
     * @param {number} beta - Beta 值
     * @param {boolean} maximizingPlayer - 是否為最大化玩家
     * @returns {Object} 最佳下法和分數
     */
    minimax(game, depth, alpha, beta, maximizingPlayer) {
        if (depth === 0) {
            return { score: this.evaluateBoardPosition(game) };
        }

        const validMoves = this.getValidMoves(game);
        if (validMoves.length === 0) {
            return { score: this.evaluateBoardPosition(game) };
        }

        // 限制搜尋範圍以提升效能
        const limitedMoves = this.selectBestMoves(game, validMoves, Math.min(15, validMoves.length));
        let bestMove = null;

        if (maximizingPlayer) {
            let maxEval = -Infinity;

            for (const [row, col] of limitedMoves) {
                // 使用簡化評估而不是完整模擬
                const moveScore = this.evaluatePosition(game, row, col);

                if (moveScore > maxEval) {
                    maxEval = moveScore;
                    bestMove = { row, col, score: maxEval };
                }

                alpha = Math.max(alpha, moveScore);
                if (beta <= alpha) {
                    break; // Alpha-beta 剪枝
                }
            }

            return bestMove || { score: maxEval };
        } else {
            let minEval = Infinity;

            for (const [row, col] of limitedMoves) {
                // 使用簡化評估
                const moveScore = -this.evaluatePosition(game, row, col); // 對手視角

                if (moveScore < minEval) {
                    minEval = moveScore;
                    bestMove = { row, col, score: minEval };
                }

                beta = Math.min(beta, moveScore);
                if (beta <= alpha) {
                    break; // Alpha-beta 剪枝
                }
            }

            return bestMove || { score: minEval };
        }
    }

    /**
     * 選擇最有希望的下法進行深度搜尋
     * @param {GoGame} game - 遊戲實例
     * @param {Array} validMoves - 有效下法陣列
     * @param {number} count - 選擇數量
     * @returns {Array} 篩選後的下法陣列
     */
    selectBestMoves(game, validMoves, count) {
        const scoredMoves = validMoves.map(([row, col]) => {
            const score = this.evaluatePosition(game, row, col);
            return { row, col, score };
        });

        // 排序並選擇最佳的幾個
        scoredMoves.sort((a, b) => b.score - a.score);
        return scoredMoves.slice(0, count).map(move => [move.row, move.col]);
    }

    /**
     * 評估整個棋盤局面
     * @param {GoGame} game - 遊戲實例
     * @returns {number} 棋盤評估分數
     */
    evaluateBoardPosition(game) {
        let score = 0;

        // 計算雙方棋子數量
        let myStones = 0;
        let opponentStones = 0;

        for (let row = 0; row < game.size; row++) {
            for (let col = 0; col < game.size; col++) {
                if (game.board[row][col] === this.color) {
                    myStones++;
                } else if (game.board[row][col] === this.opponentColor) {
                    opponentStones++;
                }
            }
        }

        // 基本分數差
        score += (myStones - opponentStones) * 10;

        // 評估領域控制
        score += this.evaluateTerritoryControl(game);

        // 評估棋子活力 (氣的數量)
        score += this.evaluateStoneLiberty(game);

        return score;
    }

    /**
     * 評估位置分數 (基礎版本)
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 位置分數
     */
    evaluatePosition(game, row, col) {
        let score = 0;

        // 圍棋位置價值評估：角落 > 邊線 > 中央
        score += this.evaluatePositionValue(game, row, col);

        // 進攻性評估 - 優先考慮進攻
        score += this.evaluateAggression(game, row, col);

        // 靠近對手棋子大幅加分（積極接觸戰）
        const adjacentPositions = game.getAdjacentPositions(row, col);
        adjacentPositions.forEach(([r, c]) => {
            if (game.board[r][c] === this.opponentColor) {
                score += 30; // 大幅提高接觸對手的分數
            } else if (game.board[r][c] === this.color) {
                score += 8; // 連接己方棋子
            }
        });

        // 檢查是否能吃子（最高優先級）
        const captureScore = this.evaluateCapture(game, row, col);
        score += captureScore * 200; // 大幅提高吃子分數

        // 檢查是否能攻擊對手（新增）
        const attackScore = this.evaluateAttack(game, row, col);
        score += attackScore * 100; // 攻擊對手很重要

        // 檢查是否能救自己的棋子
        const rescueScore = this.evaluateRescue(game, row, col);
        score += rescueScore * 60; // 救子重要但次於進攻

        // 檢查是否會被對手吃掉（負分）
        const dangerScore = this.evaluateDanger(game, row, col);
        score -= dangerScore * 40; // 提高危險懲罰

        return score;
    }

    /**
     * 評估位置的基本價值
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 位置價值分數
     */
    evaluatePositionValue(game, row, col) {
        const size = game.size;
        let score = 0;

        // 計算到邊緣的距離
        const edgeDistance = Math.min(row, col, size - 1 - row, size - 1 - col);

        // 開局階段（前30手）：角落和邊線最重要
        if (game.moveCount < 30) {
            // 角落位置（最有價值）- 根據棋盤大小調整
            const cornerSize = size >= 19 ? 4 : (size >= 13 ? 3 : 2);
            if ((row <= cornerSize && col <= cornerSize) ||
                (row <= cornerSize && col >= size - cornerSize - 1) ||
                (row >= size - cornerSize - 1 && col <= cornerSize) ||
                (row >= size - cornerSize - 1 && col >= size - cornerSize - 1)) {
                score += 20;
            }
            // 邊線位置
            else if (edgeDistance <= 3) {
                score += 10;
            }
            // 中央位置（開局價值較低）
            else {
                score += 1;
            }
        }
        // 中盤階段：中央價值提升
        else {
            if (edgeDistance <= 2) {
                score += 8; // 邊線仍有價值
            } else {
                score += 12; // 中央價值提升
            }
        }

        return score;
    }

    /**
     * 評估位置分數 (高級版本)
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 位置分數
     */
    evaluatePositionAdvanced(game, row, col) {
        let score = this.evaluatePosition(game, row, col);
        
        // 形狀評估
        score += this.evaluateShape(game, row, col) * 5;
        
        // 領域評估
        score += this.evaluateTerritory(game, row, col) * 3;
        
        // 眼位評估
        score += this.evaluateEye(game, row, col) * 8;
        
        return score;
    }

    /**
     * 評估吃子機會
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 吃子分數
     */
    evaluateCapture(game, row, col) {
        // 暫時放置棋子
        game.board[row][col] = this.color;
        
        let captureCount = 0;
        const adjacentPositions = game.getAdjacentPositions(row, col);
        
        adjacentPositions.forEach(([r, c]) => {
            if (game.board[r][c] === this.opponentColor) {
                const group = game.getGroup(r, c, this.opponentColor);
                if (game.getLiberties(group) === 0) {
                    captureCount += group.length;
                }
            }
        });
        
        // 移除暫時放置的棋子
        game.board[row][col] = 0;
        
        return captureCount;
    }

    /**
     * 評估救子機會
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 救子分數
     */
    evaluateRescue(game, row, col) {
        // 暫時放置棋子
        game.board[row][col] = this.color;
        
        let rescueScore = 0;
        const adjacentPositions = game.getAdjacentPositions(row, col);
        
        adjacentPositions.forEach(([r, c]) => {
            if (game.board[r][c] === this.color) {
                const group = game.getGroup(r, c, this.color);
                const liberties = game.getLiberties(group);
                
                // 如果這步棋能增加己方棋子的氣
                if (liberties > 0) {
                    rescueScore += group.length / liberties;
                }
            }
        });
        
        // 移除暫時放置的棋子
        game.board[row][col] = 0;
        
        return rescueScore;
    }

    /**
     * 評估形狀
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 形狀分數
     */
    evaluateShape(game, row, col) {
        // 簡單的形狀評估：避免愚形，鼓勵好形
        let shapeScore = 0;
        
        // 檢查是否形成連接
        const adjacentFriendly = game.getAdjacentPositions(row, col)
            .filter(([r, c]) => game.board[r][c] === this.color).length;
        
        shapeScore += adjacentFriendly * 2;
        
        return shapeScore;
    }

    /**
     * 評估領域控制
     * @param {GoGame} game - 遊戲實例
     * @returns {number} 領域控制分數
     */
    evaluateTerritoryControl(game) {
        let score = 0;
        const visited = Array(game.size).fill().map(() => Array(game.size).fill(false));

        for (let row = 0; row < game.size; row++) {
            for (let col = 0; col < game.size; col++) {
                if (game.board[row][col] === 0 && !visited[row][col]) {
                    const territory = this.getTerritory(game, row, col, visited);
                    if (territory.owner === this.color) {
                        score += territory.size * 2;
                    } else if (territory.owner === this.opponentColor) {
                        score -= territory.size * 2;
                    }
                }
            }
        }

        return score;
    }

    /**
     * 評估棋子活力
     * @param {GoGame} game - 遊戲實例
     * @returns {number} 活力分數
     */
    evaluateStoneLiberty(game) {
        let score = 0;
        const visited = Array(game.size).fill().map(() => Array(game.size).fill(false));

        for (let row = 0; row < game.size; row++) {
            for (let col = 0; col < game.size; col++) {
                if (!visited[row][col] && game.board[row][col] !== 0) {
                    const group = game.getGroup(row, col, game.board[row][col]);
                    const liberties = game.getLiberties(group);

                    // 標記群組為已訪問
                    group.forEach(([r, c]) => {
                        visited[r][c] = true;
                    });

                    if (game.board[row][col] === this.color) {
                        score += liberties * group.length;
                    } else {
                        score -= liberties * group.length;
                    }
                }
            }
        }

        return score;
    }

    /**
     * 取得領域資訊
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 起始行座標
     * @param {number} col - 起始列座標
     * @param {Array} visited - 已訪問陣列
     * @returns {Object} 領域資訊
     */
    getTerritory(game, row, col, visited) {
        const territory = [];
        const stack = [[row, col]];
        const borders = new Set();

        while (stack.length > 0) {
            const [r, c] = stack.pop();

            if (r < 0 || r >= game.size || c < 0 || c >= game.size || visited[r][c]) {
                continue;
            }

            if (game.board[r][c] !== 0) {
                borders.add(game.board[r][c]);
                continue;
            }

            visited[r][c] = true;
            territory.push([r, c]);

            // 檢查四個方向
            stack.push([r-1, c], [r+1, c], [r, c-1], [r, c+1]);
        }

        // 判斷領域歸屬
        let owner = null;
        if (borders.size === 1) {
            owner = Array.from(borders)[0];
        }

        return {
            size: territory.length,
            owner: owner,
            positions: territory
        };
    }

    /**
     * 評估領域 (單點)
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 領域分數
     */
    evaluateTerritory(game, row, col) {
        let score = 0;
        const radius = 3;

        // 檢查周圍區域的控制情況
        for (let r = Math.max(0, row - radius); r < Math.min(game.size, row + radius + 1); r++) {
            for (let c = Math.max(0, col - radius); c < Math.min(game.size, col + radius + 1); c++) {
                const distance = Math.abs(r - row) + Math.abs(c - col);
                const weight = Math.max(0, radius - distance + 1);

                if (game.board[r][c] === this.color) {
                    score += weight;
                } else if (game.board[r][c] === this.opponentColor) {
                    score -= weight;
                }
            }
        }

        return score;
    }

    /**
     * 評估眼位
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 眼位分數
     */
    evaluateEye(game, row, col) {
        // 簡單的眼位評估
        const surroundingFriendly = game.getAdjacentPositions(row, col)
            .filter(([r, c]) => game.board[r][c] === this.color).length;

        return surroundingFriendly >= 3 ? 10 : 0;
    }

    /**
     * 評估下棋後的危險性
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 危險分數
     */
    evaluateDanger(game, row, col) {
        // 暫時放置棋子
        game.board[row][col] = this.color;

        // 檢查這個棋子所在群組的氣數
        const group = game.getGroup(row, col, this.color);
        const liberties = game.getLiberties(group);

        // 移除暫時放置的棋子
        game.board[row][col] = 0;

        // 氣數越少越危險
        if (liberties === 1) {
            return 15; // 非常危險，可能被吃
        } else if (liberties === 2) {
            return 8; // 有些危險
        } else if (liberties === 3) {
            return 3; // 稍微危險
        }

        return 0; // 安全
    }

    /**
     * 評估進攻性 - 積極尋找攻擊機會
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 進攻分數
     */
    evaluateAggression(game, row, col) {
        let aggressionScore = 0;

        // 檢查周圍是否有對手的弱棋（氣少的棋子）
        const adjacentPositions = game.getAdjacentPositions(row, col);
        adjacentPositions.forEach(([r, c]) => {
            if (game.board[r][c] === this.opponentColor) {
                const group = game.getGroup(r, c, this.opponentColor);
                const liberties = game.getLiberties(group);

                // 對手棋子氣數越少，攻擊價值越高
                if (liberties === 1) {
                    aggressionScore += 50; // 可以立即吃掉
                } else if (liberties === 2) {
                    aggressionScore += 25; // 可以攻擊
                } else if (liberties === 3) {
                    aggressionScore += 10; // 施加壓力
                }
            }
        });

        // 檢查是否能切斷對手連接
        aggressionScore += this.evaluateCutting(game, row, col) * 15;

        // 檢查是否能侵入對手領域
        aggressionScore += this.evaluateInvasion(game, row, col) * 20;

        return aggressionScore;
    }

    /**
     * 評估攻擊對手的機會
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 攻擊分數
     */
    evaluateAttack(game, row, col) {
        let attackScore = 0;

        // 暫時放置棋子
        game.board[row][col] = this.color;

        // 檢查能否減少對手棋子的氣
        const adjacentPositions = game.getAdjacentPositions(row, col);
        adjacentPositions.forEach(([r, c]) => {
            if (game.board[r][c] === this.opponentColor) {
                const group = game.getGroup(r, c, this.opponentColor);
                const liberties = game.getLiberties(group);

                // 根據減少對手氣數給分
                if (liberties === 1) {
                    attackScore += group.length * 10; // 即將吃掉
                } else if (liberties === 2) {
                    attackScore += group.length * 5; // 嚴重威脅
                } else if (liberties === 3) {
                    attackScore += group.length * 2; // 一般威脅
                }
            }
        });

        // 移除暫時放置的棋子
        game.board[row][col] = 0;

        return attackScore;
    }

    /**
     * 評估切斷對手連接的機會
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 切斷分數
     */
    evaluateCutting(game, row, col) {
        let cuttingScore = 0;

        // 檢查對角線上是否有對手棋子可以切斷
        const diagonalPositions = [
            [row - 1, col - 1], [row - 1, col + 1],
            [row + 1, col - 1], [row + 1, col + 1]
        ];

        let opponentCount = 0;
        diagonalPositions.forEach(([r, c]) => {
            if (r >= 0 && r < game.size && c >= 0 && c < game.size) {
                if (game.board[r][c] === this.opponentColor) {
                    opponentCount++;
                }
            }
        });

        // 如果對角線上有多個對手棋子，可能可以切斷
        if (opponentCount >= 2) {
            cuttingScore += opponentCount * 3;
        }

        return cuttingScore;
    }

    /**
     * 評估侵入對手領域的機會
     * @param {GoGame} game - 遊戲實例
     * @param {number} row - 行座標
     * @param {number} col - 列座標
     * @returns {number} 侵入分數
     */
    evaluateInvasion(game, row, col) {
        let invasionScore = 0;
        const radius = 4;

        // 檢查周圍區域是否主要被對手控制
        let opponentStones = 0;
        let myStones = 0;
        let totalChecked = 0;

        for (let r = Math.max(0, row - radius); r < Math.min(game.size, row + radius + 1); r++) {
            for (let c = Math.max(0, col - radius); c < Math.min(game.size, col + radius + 1); c++) {
                if (r === row && c === col) continue;

                totalChecked++;
                if (game.board[r][c] === this.opponentColor) {
                    opponentStones++;
                } else if (game.board[r][c] === this.color) {
                    myStones++;
                }
            }
        }

        // 如果對手在這個區域佔優勢，侵入有價值
        if (opponentStones > myStones && opponentStones > totalChecked * 0.3) {
            invasionScore += Math.min(10, opponentStones - myStones);
        }

        return invasionScore;
    }

    /**
     * 尋找立即吃子的機會
     * @param {GoGame} game - 遊戲實例
     * @param {Array} validMoves - 有效下法陣列
     * @returns {Array} 吃子機會陣列
     */
    findCaptureMoves(game, validMoves) {
        const captureMoves = [];

        validMoves.forEach(([row, col]) => {
            const captureCount = this.evaluateCapture(game, row, col);
            if (captureCount > 0) {
                captureMoves.push({ row, col, captureCount });
            }
        });

        return captureMoves;
    }

    /**
     * 尋找致命攻擊機會（讓對手只剩1氣）
     * @param {GoGame} game - 遊戲實例
     * @param {Array} validMoves - 有效下法陣列
     * @returns {Array} 致命攻擊機會陣列
     */
    findKillMoves(game, validMoves) {
        const killMoves = [];

        validMoves.forEach(([row, col]) => {
            // 暫時放置棋子
            game.board[row][col] = this.color;

            const adjacentPositions = game.getAdjacentPositions(row, col);
            adjacentPositions.forEach(([r, c]) => {
                if (game.board[r][c] === this.opponentColor) {
                    const group = game.getGroup(r, c, this.opponentColor);
                    const liberties = game.getLiberties(group);

                    // 如果對手群組只剩1氣，這是致命攻擊
                    if (liberties === 1) {
                        killMoves.push({
                            row,
                            col,
                            targetSize: group.length,
                            liberties: liberties
                        });
                    }
                }
            });

            // 移除暫時放置的棋子
            game.board[row][col] = 0;
        });

        return killMoves;
    }

    /**
     * 尋找威脅機會（讓對手只剩2氣）
     * @param {GoGame} game - 遊戲實例
     * @param {Array} validMoves - 有效下法陣列
     * @returns {Array} 威脅機會陣列
     */
    findThreatMoves(game, validMoves) {
        const threatMoves = [];

        validMoves.forEach(([row, col]) => {
            // 暫時放置棋子
            game.board[row][col] = this.color;

            let threatValue = 0;
            const adjacentPositions = game.getAdjacentPositions(row, col);
            adjacentPositions.forEach(([r, c]) => {
                if (game.board[r][c] === this.opponentColor) {
                    const group = game.getGroup(r, c, this.opponentColor);
                    const liberties = game.getLiberties(group);

                    // 如果對手群組只剩2氣，這是嚴重威脅
                    if (liberties === 2) {
                        threatValue += group.length * 5;
                    } else if (liberties === 3) {
                        threatValue += group.length * 2;
                    }
                }
            });

            if (threatValue > 0) {
                threatMoves.push({ row, col, threatValue });
            }

            // 移除暫時放置的棋子
            game.board[row][col] = 0;
        });

        return threatMoves;
    }

    /**
     * 尋找攻擊機會
     * @param {GoGame} game - 遊戲實例
     * @param {Array} validMoves - 有效下法陣列
     * @returns {Array} 攻擊機會陣列
     */
    findAttackMoves(game, validMoves) {
        const attackMoves = [];

        validMoves.forEach(([row, col]) => {
            const attackValue = this.evaluateAttack(game, row, col);
            if (attackValue > 0) {
                attackMoves.push({ row, col, attackValue });
            }
        });

        return attackMoves;
    }

    /**
     * 尋找接觸戰機會
     * @param {GoGame} game - 遊戲實例
     * @param {Array} validMoves - 有效下法陣列
     * @returns {Array} 接觸戰位置陣列
     */
    findContactMoves(game, validMoves) {
        return validMoves.filter(([row, col]) => {
            const adjacentPositions = game.getAdjacentPositions(row, col);
            return adjacentPositions.some(([r, c]) => game.board[r][c] !== 0);
        });
    }

    /**
     * 過濾近距離下法（避免過遠佈局）
     * @param {GoGame} game - 遊戲實例
     * @param {Array} validMoves - 有效下法陣列
     * @returns {Array} 近距離位置陣列
     */
    filterNearMoves(game, validMoves) {
        if (game.moveCount < 10) {
            // 開局階段允許較遠的位置
            return validMoves;
        }

        // 找到棋盤上所有棋子的位置
        const occupiedPositions = [];
        for (let row = 0; row < game.size; row++) {
            for (let col = 0; col < game.size; col++) {
                if (game.board[row][col] !== 0) {
                    occupiedPositions.push([row, col]);
                }
            }
        }

        // 只保留距離現有棋子不超過3格的位置
        return validMoves.filter(([row, col]) => {
            return occupiedPositions.some(([r, c]) => {
                const distance = Math.abs(row - r) + Math.abs(col - c);
                return distance <= 3;
            });
        });
    }

    /**
     * 限制範圍的 Minimax 搜尋
     * @param {GoGame} game - 遊戲實例
     * @param {Array} limitedMoves - 限制的下法陣列
     * @param {number} depth - 搜尋深度
     * @returns {Object} 最佳下法
     */
    minimaxLimited(game, limitedMoves, depth) {
        const scoredMoves = limitedMoves.map(([row, col]) => {
            const score = this.evaluatePositionAdvanced(game, row, col);
            return { row, col, score };
        });

        // 排序並選擇最佳的幾個進行深度搜尋
        scoredMoves.sort((a, b) => b.score - a.score);
        const topMoves = scoredMoves.slice(0, Math.min(8, scoredMoves.length));

        if (topMoves.length > 0) {
            return { row: topMoves[0].row, col: topMoves[0].col };
        }

        return null;
    }
}
