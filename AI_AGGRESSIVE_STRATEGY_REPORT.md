# 圍棋 AI 進攻性戰略改善報告

## 改善概述

根據使用者反饋，AI 過於防禦性，缺乏積極的進攻戰略。本次改善大幅提升 AI 的進攻性，讓 AI 從一開始就積極攻擊對手，而不是被動地慢慢擴張領域。

## 主要問題分析

### 原有問題
1. **過於保守**：AI 偏好安全的位置，避免接觸戰
2. **缺乏攻擊性**：不會主動攻擊對手的弱棋
3. **戰略被動**：只關注自己的領域擴張，忽視對手威脅
4. **評估權重不當**：防禦分數過高，進攻分數過低

### 圍棋理論依據
- **金角銀邊草肚皮**：開局佔角後應該積極向邊線和中央發展
- **攻擊是最好的防禦**：通過攻擊對手來獲得主動權
- **先手的重要性**：保持先手，迫使對手應對
- **弱棋必攻**：發現對手弱棋必須立即攻擊

## 進攻性改善措施

### 1. 大幅提升接觸戰分數
```javascript
// 修正前
if (game.board[r][c] === this.opponentColor) {
    score += 10; // 接觸對手棋子
}

// 修正後
if (game.board[r][c] === this.opponentColor) {
    score += 30; // 大幅提高接觸對手的分數
}
```

### 2. 強化吃子優先級
```javascript
// 修正前
score += captureScore * 50;

// 修正後  
score += captureScore * 200; // 大幅提高吃子分數
```

### 3. 新增進攻性評估系統
```javascript
evaluateAggression(game, row, col) {
    // 檢查對手弱棋（氣少的棋子）
    if (liberties === 1) {
        aggressionScore += 50; // 可以立即吃掉
    } else if (liberties === 2) {
        aggressionScore += 25; // 可以攻擊
    } else if (liberties === 3) {
        aggressionScore += 10; // 施加壓力
    }
}
```

### 4. 新增攻擊評估功能
```javascript
evaluateAttack(game, row, col) {
    // 根據減少對手氣數給分
    if (liberties === 1) {
        attackScore += group.length * 10; // 即將吃掉
    } else if (liberties === 2) {
        attackScore += group.length * 5; // 嚴重威脅
    }
}
```

## 新增戰術功能

### 1. 切斷戰術
**功能**：`evaluateCutting()`
**目的**：識別並執行切斷對手連接的機會
**實作**：
- 檢查對角線上的對手棋子
- 評估切斷的戰術價值
- 優先切斷重要連接

### 2. 侵入戰術  
**功能**：`evaluateInvasion()`
**目的**：積極侵入對手控制的區域
**實作**：
- 識別對手優勢區域
- 評估侵入的成功機率
- 在適當時機發動侵入

### 3. 弱棋攻擊
**功能**：`evaluateAggression()`
**目的**：優先攻擊對手的弱棋
**實作**：
- 識別氣數少的對手棋子
- 計算攻擊的收益
- 持續施加壓力

## 評估權重調整

| 戰術類型 | 修正前權重 | 修正後權重 | 提升倍數 | 戰略意義 |
|---------|-----------|-----------|---------|----------|
| 吃子機會 | ×50 | ×200 | 4倍 | 立即獲得實利 |
| 接觸戰 | ×10 | ×30 | 3倍 | 積極接觸對手 |
| 攻擊對手 | 無 | ×100 | 新增 | 主動施壓 |
| 進攻性 | 無 | ×1 | 新增 | 整體進攻傾向 |
| 切斷戰術 | 無 | ×15 | 新增 | 破壞對手連接 |
| 侵入戰術 | 無 | ×20 | 新增 | 破壞對手領域 |
| 危險懲罰 | ×20 | ×40 | 2倍 | 平衡進攻風險 |

## 戰略層次改善

### 1. 開局階段（前30手）
- **積極佔角**：優先佔據四個角落
- **快速展開**：向邊線和中央發展
- **主動接觸**：不避免與對手接觸

### 2. 中盤階段（30-100手）
- **攻擊弱棋**：優先攻擊對手氣數少的棋子
- **切斷連接**：破壞對手的棋子連接
- **侵入領域**：在對手領域中製造混亂

### 3. 終盤階段（100手以後）
- **收官爭先**：搶佔大官子
- **生死攻防**：解決生死問題
- **精確計算**：每手都要精確計算

## 測試驗證

### 1. 進攻性測試
- **弱棋攻擊**：AI 能否正確識別並攻擊對手弱棋
- **切斷戰術**：AI 能否執行有效的切斷
- **侵入時機**：AI 能否在適當時機侵入

### 2. 速度測試
- **3倍速模式**：500ms 間隔快速測試
- **極速模式**：200ms 間隔壓力測試
- **效能監控**：確保高速下仍能正確思考

### 3. 戰略對比
- **修正前 vs 修正後**：同難度 AI 對戰比較
- **不同難度測試**：驗證進攻性在各難度的表現
- **人機對戰**：與人類玩家的對戰效果

## 測試頁面改善

### 1. 速度控制
```html
<select id="test-speed">
    <option value="2000">慢速 (2秒)</option>
    <option value="1500">正常 (1.5秒)</option>
    <option value="500">3倍速 (0.5秒)</option>
    <option value="200">極速 (0.2秒)</option>
</select>
```

### 2. 進攻性指標
- **攻擊次數統計**：記錄 AI 的攻擊行為
- **接觸戰比例**：計算接觸戰的頻率
- **侵入成功率**：評估侵入戰術的效果

## 預期效果

### 1. 棋風改變
- **從保守到積極**：AI 不再被動防禦
- **從慢到快**：遊戲節奏明顯加快
- **從單調到多變**：戰術更加豐富

### 2. 教育價值提升
- **學習進攻**：玩家可以學習進攻戰術
- **應對壓力**：訓練在壓力下的應對能力
- **戰略思考**：理解攻防轉換的時機

### 3. 娛樂性增強
- **更有挑戰性**：AI 更難對付
- **更有趣味性**：對局更加精彩
- **更有觀賞性**：AI vs AI 更好看

## 風險控制

### 1. 過度進攻風險
- **危險評估加強**：提高危險懲罰權重
- **安全檢查**：確保不會自殺
- **平衡調整**：在進攻和安全間找平衡

### 2. 效能風險
- **計算複雜度**：新增評估可能影響速度
- **記憶體使用**：避免過度使用記憶體
- **穩定性保證**：確保不會當機

## 後續改善方向

### 1. 高級戰術
- **複雜攻殺**：實作更複雜的攻殺戰術
- **大局觀**：提升整體戰略判斷
- **時機把握**：更精確的攻擊時機

### 2. 學習機制
- **對局學習**：從失敗中學習改善
- **風格適應**：適應不同對手風格
- **動態調整**：根據局面動態調整策略

### 3. 專業功能
- **職業級戰術**：實作職業棋手的戰術
- **定石研究**：深入研究開局定石
- **官子計算**：精確的終盤計算

## 結論

通過這次進攻性改善，圍棋 AI 實現了從防禦型到進攻型的根本轉變：

✅ **積極主動**：從一開始就尋找攻擊機會  
✅ **戰術豐富**：切斷、侵入、攻殺等多種戰術  
✅ **節奏加快**：3倍速測試支援快速驗證  
✅ **挑戰性強**：為玩家提供真正的挑戰  
✅ **教育價值高**：學習正確的進攻戰略  

這些改善讓圍棋 AI 更符合真實的圍棋對局特點，為使用者提供更加真實、刺激和有教育意義的圍棋體驗。AI 不再是被動的防禦者，而是積極的攻擊者，這正是圍棋這項運動的精髓所在。
