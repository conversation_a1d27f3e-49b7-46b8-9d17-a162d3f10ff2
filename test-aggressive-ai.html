<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>積極進攻 AI 測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5dc;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-board {
            margin: 20px 0;
            text-align: center;
        }
        canvas {
            border: 2px solid #8B4513;
            border-radius: 5px;
        }
        .test-info {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .test-buttons {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #8B4513;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #A0522D;
        }
        .aggressive-test {
            background: #ffebee;
            border: 2px solid #f44336;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .aggressive-test h3 {
            color: #d32f2f;
            margin-top: 0;
        }
        .behavior-log {
            height: 200px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ccc;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
        .metrics {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border: 2px solid #ddd;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #d32f2f;
        }
        .metric-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>積極進攻 AI 測試</h1>
        
        <div class="test-info">
            <h3>測試目標：</h3>
            <p>1. 驗證高級和專家 AI 是否優先吃子</p>
            <p>2. 檢查 AI 是否避免過遠佈局</p>
            <p>3. 測試 AI 的攻擊積極性</p>
            <p>4. 觀察接觸戰和威脅行為</p>
        </div>
        
        <div class="test-board">
            <canvas id="test-board" width="500" height="500"></canvas>
        </div>
        
        <div class="status">
            <div>當前玩家: <span id="current-player">黑子</span></div>
            <div>手數: <span id="move-count">0</span></div>
            <div>最後訊息: <span id="last-message">開始測試</span></div>
        </div>
        
        <div class="test-buttons">
            <button onclick="resetTest()">重置測試</button>
            <button onclick="startAggressiveTest('hard', 'expert')">高級 vs 專家</button>
            <button onclick="startAggressiveTest('medium', 'hard')">中級 vs 高級</button>
            <button onclick="startAggressiveTest('expert', 'expert')">專家 vs 專家</button>
            <button onclick="setupCaptureScenario()">設置吃子場景</button>
            <button onclick="pauseTest()">暫停/繼續</button>
        </div>
        
        <div class="speed-controls" style="text-align: center; margin: 15px 0;">
            <label for="test-speed">測試速度: </label>
            <select id="test-speed" onchange="updateTestSpeed()">
                <option value="1000">正常 (1秒)</option>
                <option value="500" selected>3倍速 (0.5秒)</option>
                <option value="200">極速 (0.2秒)</option>
            </select>
        </div>
        
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-value" id="capture-count">0</div>
                <div class="metric-label">吃子次數</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="contact-ratio">0%</div>
                <div class="metric-label">接觸戰比例</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="avg-distance">0</div>
                <div class="metric-label">平均下子距離</div>
            </div>
        </div>
        
        <div class="aggressive-test">
            <h3>積極性行為記錄</h3>
            <div class="behavior-log" id="behavior-log"></div>
        </div>
        
        <div class="test-info">
            <h3>評估標準：</h3>
            <p><strong>優先吃子：</strong>有吃子機會時必須立即執行</p>
            <p><strong>避免遠子：</strong>不下距離現有棋子超過3格的位置</p>
            <p><strong>積極接觸：</strong>優先選擇與對手棋子相鄰的位置</p>
            <p><strong>攻擊威脅：</strong>主動減少對手棋子的氣數</p>
        </div>
    </div>

    <!-- 載入圍棋遊戲模組 -->
    <script src="js/go-game.js"></script>
    <script src="js/go-board.js"></script>
    <script src="js/go-ai.js"></script>
    
    <script>
        // 測試用的遊戲實例
        let testGame = new GoGame(13);
        let testBoard = new GoBoard(document.getElementById('test-board'), 13);
        let blackAi = null;
        let whiteAi = null;
        let isRunning = false;
        let isPaused = false;
        let testSpeed = 500;
        
        // 統計數據
        let captureCount = 0;
        let contactMoves = 0;
        let totalMoves = 0;
        let totalDistance = 0;
        
        function updateStatus() {
            document.getElementById('current-player').textContent = 
                testGame.currentPlayer === 1 ? '黑子' : '白子';
            document.getElementById('move-count').textContent = testGame.moveCount;
        }
        
        function updateMetrics() {
            document.getElementById('capture-count').textContent = captureCount;
            const contactRatio = totalMoves > 0 ? Math.round((contactMoves / totalMoves) * 100) : 0;
            document.getElementById('contact-ratio').textContent = contactRatio + '%';
            const avgDistance = totalMoves > 0 ? (totalDistance / totalMoves).toFixed(1) : 0;
            document.getElementById('avg-distance').textContent = avgDistance;
        }
        
        function logBehavior(message) {
            const log = document.getElementById('behavior-log');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }
        
        function resetTest() {
            testGame.reset();
            testBoard.clear();
            blackAi = null;
            whiteAi = null;
            isRunning = false;
            isPaused = false;
            
            // 重置統計
            captureCount = 0;
            contactMoves = 0;
            totalMoves = 0;
            totalDistance = 0;
            
            updateStatus();
            updateMetrics();
            document.getElementById('last-message').textContent = '測試重置';
            document.getElementById('behavior-log').innerHTML = '';
        }
        
        function startAggressiveTest(blackDifficulty, whiteDifficulty) {
            resetTest();
            
            blackAi = new GoAI(blackDifficulty, 1, 13);
            whiteAi = new GoAI(whiteDifficulty, 2, 13);
            
            isRunning = true;
            isPaused = false;
            
            logBehavior(`開始積極性測試：${blackDifficulty} vs ${whiteDifficulty}`);
            document.getElementById('last-message').textContent = 
                `開始測試：${blackDifficulty} vs ${whiteDifficulty}`;
            
            setTimeout(makeAiMove, 1000);
        }
        
        async function makeAiMove() {
            if (!isRunning || testGame.gameEnded) {
                return;
            }
            
            if (isPaused) {
                setTimeout(makeAiMove, 100);
                return;
            }
            
            const currentAi = testGame.currentPlayer === 1 ? blackAi : whiteAi;
            const aiName = testGame.currentPlayer === 1 ? '黑子' : '白子';
            
            try {
                const aiMove = await currentAi.chooseMove(testGame);
                
                if (aiMove.pass) {
                    testGame.pass();
                    logBehavior(`${aiName} Pass`);
                } else {
                    // 分析這步棋的特性
                    const moveAnalysis = analyzeMoveAggression(aiMove.row, aiMove.col);
                    
                    const result = testGame.makeMove(aiMove.row, aiMove.col);
                    if (result.success) {
                        // 同步棋盤狀態
                        testBoard.setBoardState(testGame.board);
                        testBoard.lastMove = [aiMove.row, aiMove.col];
                        testBoard.drawBoard();
                        
                        // 更新統計
                        totalMoves++;
                        if (result.captured > 0) {
                            captureCount++;
                            logBehavior(`${aiName} 在 ${result.notation} 吃掉 ${result.captured} 子！`);
                        }
                        if (moveAnalysis.isContact) {
                            contactMoves++;
                            logBehavior(`${aiName} 在 ${result.notation} 進行接觸戰`);
                        }
                        if (moveAnalysis.distance > 3) {
                            logBehavior(`${aiName} 在 ${result.notation} 下了遠子 (距離: ${moveAnalysis.distance})`);
                        }
                        totalDistance += moveAnalysis.distance;
                        
                        updateMetrics();
                        updateStatus();
                        
                        document.getElementById('last-message').textContent = 
                            `${aiName} 下在 ${result.notation}`;
                    }
                }
                
                if (testGame.gameEnded) {
                    logBehavior('遊戲結束');
                    isRunning = false;
                    return;
                }
                
                if (!isPaused) {
                    setTimeout(makeAiMove, testSpeed);
                }
                
            } catch (error) {
                console.error(`${aiName} 下棋錯誤:`, error);
                logBehavior(`${aiName} 出錯: ${error.message}`);
            }
        }
        
        function analyzeMoveAggression(row, col) {
            // 檢查是否為接觸戰
            const adjacentPositions = testGame.getAdjacentPositions(row, col);
            const isContact = adjacentPositions.some(([r, c]) => testGame.board[r][c] !== 0);
            
            // 計算到最近棋子的距離
            let minDistance = Infinity;
            for (let r = 0; r < testGame.size; r++) {
                for (let c = 0; c < testGame.size; c++) {
                    if (testGame.board[r][c] !== 0) {
                        const distance = Math.abs(row - r) + Math.abs(col - c);
                        minDistance = Math.min(minDistance, distance);
                    }
                }
            }
            
            return {
                isContact: isContact,
                distance: minDistance === Infinity ? 0 : minDistance
            };
        }
        
        function setupCaptureScenario() {
            resetTest();
            
            // 設置一個有多個吃子機會的場景
            testGame.board[5][5] = 2; // 白子
            testGame.board[4][5] = 1; // 黑子
            testGame.board[6][5] = 1; // 黑子
            testGame.board[5][4] = 1; // 黑子
            // [5][6] 位置空著，白子只剩一口氣
            
            testGame.board[8][8] = 1; // 黑子
            testGame.board[7][8] = 2; // 白子
            testGame.board[9][8] = 2; // 白子
            testGame.board[8][7] = 2; // 白子
            // [8][9] 位置空著，黑子只剩一口氣
            
            testGame.currentPlayer = 1; // 黑子先行
            testGame.moveCount = 8;
            
            // 同步棋盤顯示
            testBoard.setBoardState(testGame.board);
            testBoard.drawBoard();
            
            logBehavior('設置吃子場景：黑子可以在 (5,6) 吃白子，白子可以在 (8,9) 吃黑子');
            document.getElementById('last-message').textContent = '吃子場景已設置';
        }
        
        function pauseTest() {
            isPaused = !isPaused;
            document.getElementById('last-message').textContent = 
                isPaused ? '測試暫停' : '測試繼續';
            
            if (!isPaused && isRunning) {
                setTimeout(makeAiMove, 100);
            }
        }
        
        function updateTestSpeed() {
            const speedSelect = document.getElementById('test-speed');
            testSpeed = parseInt(speedSelect.value);
            logBehavior(`測試速度調整為 ${testSpeed}ms`);
        }
        
        // 初始化
        updateStatus();
        updateMetrics();
    </script>
</body>
</html>
