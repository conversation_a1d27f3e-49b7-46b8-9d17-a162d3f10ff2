# 專家級和高級 AI 積極進攻改善報告

## 改善概述

針對使用者反饋，對專家級和高級 AI 進行了特別的積極性改善，確保它們：
1. **絕對優先吃子** - 看到可以吃的子立即執行
2. **避免過遠佈局** - 不下距離現有棋子過遠的位置
3. **專注戰鬥** - 優先接觸戰和攻擊行為

## 核心改善策略

### 1. 重新設計決策優先級

#### 專家級 AI (`chooseExpertMove`) 決策順序：
```javascript
1. 立即吃子（絕對優先）
   ↓
2. 致命攻擊（讓對手只剩1氣）
   ↓  
3. 嚴重威脅（讓對手只剩2氣）
   ↓
4. 限制範圍的 Minimax 搜尋
   ↓
5. 避免過遠佈局的戰鬥選擇
```

#### 高級 AI (`chooseAdvancedMove`) 決策順序：
```javascript
1. 立即吃子機會
   ↓
2. 攻擊機會（減少對手氣數）
   ↓
3. 接觸戰機會
   ↓
4. 近距離戰鬥位置
```

### 2. 新增專門的戰術識別功能

#### 2.1 立即吃子識別 (`findCaptureMoves`)
```javascript
// 掃描所有位置，找出能立即吃子的機會
validMoves.forEach(([row, col]) => {
    const captureCount = this.evaluateCapture(game, row, col);
    if (captureCount > 0) {
        captureMoves.push({ row, col, captureCount });
    }
});
```

#### 2.2 致命攻擊識別 (`findKillMoves`)
```javascript
// 找出能讓對手棋子只剩1氣的位置
if (liberties === 1) {
    killMoves.push({ 
        row, col, 
        targetSize: group.length,
        liberties: liberties
    });
}
```

#### 2.3 威脅攻擊識別 (`findThreatMoves`)
```javascript
// 找出能讓對手棋子只剩2-3氣的位置
if (liberties === 2) {
    threatValue += group.length * 5;
} else if (liberties === 3) {
    threatValue += group.length * 2;
}
```

### 3. 距離控制機制

#### 3.1 接觸戰優先 (`findContactMoves`)
```javascript
// 只選擇與現有棋子相鄰的位置
return validMoves.filter(([row, col]) => {
    const adjacentPositions = game.getAdjacentPositions(row, col);
    return adjacentPositions.some(([r, c]) => game.board[r][c] !== 0);
});
```

#### 3.2 近距離過濾 (`filterNearMoves`)
```javascript
// 過濾掉距離現有棋子超過3格的位置
return validMoves.filter(([row, col]) => {
    return occupiedPositions.some(([r, c]) => {
        const distance = Math.abs(row - r) + Math.abs(col - c);
        return distance <= 3;
    });
});
```

## 行為模式對比

### 修正前的問題行為
- ❌ 有吃子機會但選擇其他位置
- ❌ 下在距離戰場很遠的角落
- ❌ 優先考慮領域擴張而非戰鬥
- ❌ 被動等待對手攻擊

### 修正後的積極行為
- ✅ 絕對優先執行吃子
- ✅ 主動攻擊對手弱棋
- ✅ 專注於接觸戰和近距離戰鬥
- ✅ 持續施加壓力

## 技術實作細節

### 1. 吃子優先級實作
```javascript
// 專家級：絕對優先吃子
const captureMoves = this.findCaptureMoves(game, validMoves);
if (captureMoves.length > 0) {
    // 選擇能吃最多子的位置，不考慮其他因素
    captureMoves.sort((a, b) => b.captureCount - a.captureCount);
    return { row: captureMoves[0].row, col: captureMoves[0].col };
}
```

### 2. 攻擊性評估強化
```javascript
// 致命攻擊：讓對手只剩1氣
if (liberties === 1) {
    killMoves.push({ 
        row, col, 
        targetSize: group.length,
        liberties: liberties
    });
}
```

### 3. 距離限制實作
```javascript
// 開局後只考慮距離現有棋子3格內的位置
if (game.moveCount >= 10) {
    const distance = Math.abs(row - r) + Math.abs(col - c);
    return distance <= 3;
}
```

## 測試驗證系統

### 1. 專門測試頁面 (`test-aggressive-ai.html`)
- **吃子優先測試**：設置吃子場景驗證 AI 行為
- **距離統計**：記錄 AI 下子的平均距離
- **接觸戰比例**：統計接觸戰的頻率
- **行為記錄**：詳細記錄每個積極行為

### 2. 測試指標
| 指標 | 目標值 | 說明 |
|------|--------|------|
| 吃子優先率 | 100% | 有吃子機會時必須執行 |
| 接觸戰比例 | >70% | 大部分下法應該是接觸戰 |
| 平均距離 | <2.5 | 避免過遠的佈局子 |
| 攻擊頻率 | >50% | 經常性的攻擊行為 |

### 3. 場景測試
- **吃子場景**：多個吃子機會的複雜局面
- **攻擊場景**：需要連續攻擊的局面
- **混戰場景**：複雜的接觸戰局面

## 預期效果

### 1. 戰鬥力提升
- **立即威脅**：對手必須立即應對
- **持續壓力**：不給對手喘息機會
- **戰術豐富**：多種攻擊手段

### 2. 遊戲節奏加快
- **快速決戰**：避免冗長的佈局階段
- **激烈對抗**：更多的戰鬥和吃子
- **觀賞性強**：更精彩的對局

### 3. 教育價值
- **學習攻擊**：玩家學習正確的攻擊時機
- **戰術訓練**：面對積極 AI 的應對訓練
- **實戰經驗**：更接近真實對局的體驗

## 風險控制

### 1. 過度攻擊風險
- **安全檢查**：確保攻擊不會自殺
- **形勢判斷**：在劣勢時適度保守
- **平衡調整**：攻擊和防禦的平衡

### 2. 效能考量
- **搜尋限制**：限制搜尋範圍提升速度
- **快取機制**：避免重複計算
- **優化演算法**：提升評估效率

## 使用者體驗改善

### 1. 挑戰性提升
- **真正的對手**：不再是被動的 AI
- **學習價值**：從 AI 的攻擊中學習
- **成就感**：戰勝積極 AI 更有成就感

### 2. 遊戲樂趣
- **刺激對局**：每手都可能有戰鬥
- **快節奏**：不會有冗長的佈局
- **變化豐富**：每局都有不同的戰鬥

## 後續改善方向

### 1. 更精細的攻擊判斷
- **生死計算**：更準確的生死判斷
- **劫爭處理**：複雜劫爭的攻擊策略
- **官子判斷**：終盤階段的攻擊選擇

### 2. 動態策略調整
- **局面適應**：根據局面調整攻擊強度
- **對手分析**：分析對手風格調整策略
- **學習機制**：從失敗中學習改善

### 3. 專業級功能
- **職業手法**：模擬職業棋手的攻擊
- **定石攻擊**：在定石中的攻擊變化
- **大局觀**：攻擊與大局的平衡

## 結論

通過這次針對性的改善，專家級和高級 AI 實現了根本性的轉變：

✅ **絕對優先吃子** - 看到吃子機會立即執行  
✅ **避免過遠佈局** - 專注於戰場附近的戰鬥  
✅ **積極主動攻擊** - 持續尋找攻擊機會  
✅ **戰術豐富多樣** - 多種攻擊和威脅手段  
✅ **真正的挑戰性** - 為玩家提供真實的對手  

這些改善讓高級和專家 AI 成為真正積極進攻的對手，不再是被動的防禦者。它們會從一開始就尋找攻擊機會，優先吃子，避免無意義的遠距離佈局，為玩家提供更加刺激和有挑戰性的圍棋體驗。
