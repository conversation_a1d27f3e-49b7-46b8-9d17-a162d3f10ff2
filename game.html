<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>圍棋對局 - 網頁版</title>
    <link rel="stylesheet" href="css/game.css">
</head>
<body>
    <div class="game-container">
        <header class="game-header">
            <div class="header-left">
                <button id="back-to-menu-btn" class="header-btn">返回選單</button>
                <div class="game-info">
                    <span id="game-mode">單機版</span>
                    <span id="board-size">19路</span>
                </div>
            </div>
            
            <div class="header-center">
                <h1>圍棋對局</h1>
            </div>
            
            <div class="header-right">
                <button id="new-game-btn" class="header-btn">新局</button>
                <button id="settings-btn" class="header-btn">設定</button>
            </div>
        </header>
        
        <main class="game-main">
            <!-- 左側玩家資訊 -->
            <div class="player-panel left-panel">
                <div class="player-info">
                    <div class="player-avatar black">⚫</div>
                    <div class="player-details">
                        <h3 id="black-player-name">黑子</h3>
                        <div class="player-stats">
                            <div class="stat-item">
                                <span class="stat-label">俘虜:</span>
                                <span id="black-captures" class="stat-value">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">時間:</span>
                                <span id="black-time" class="stat-value">--:--</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="game-controls">
                    <button id="pass-btn" class="control-btn">Pass</button>
                    <button id="resign-btn" class="control-btn resign">認輸</button>
                </div>
                
                <div class="move-history">
                    <h4>棋譜</h4>
                    <div id="move-list" class="move-list">
                        <!-- 棋譜記錄將在這裡顯示 -->
                    </div>
                </div>
            </div>
            
            <!-- 中央棋盤區域 -->
            <div class="board-container">
                <div class="board-wrapper">
                    <canvas id="go-board" class="go-board"></canvas>
                    <div id="board-overlay" class="board-overlay"></div>
                </div>
                
                <div class="board-controls">
                    <button id="undo-btn" class="board-control-btn" disabled>悔棋</button>
                    <button id="hint-btn" class="board-control-btn">提示</button>
                    <button id="analyze-btn" class="board-control-btn">分析</button>
                </div>

                <!-- AI vs AI 專用控制 -->
                <div id="ai-vs-ai-controls" class="ai-vs-ai-controls hidden">
                    <div class="speed-control">
                        <label for="ai-battle-speed">對戰速度:</label>
                        <select id="ai-battle-speed">
                            <option value="200">極速</option>
                            <option value="500">3倍速</option>
                            <option value="1000" selected>正常</option>
                            <option value="2000">緩慢</option>
                            <option value="3000">很慢</option>
                        </select>
                    </div>
                    <button id="pause-ai-battle-btn" class="board-control-btn">暫停</button>
                    <button id="step-ai-battle-btn" class="board-control-btn">單步</button>
                </div>
                
                <!-- 遊戲狀態顯示 -->
                <div id="game-status" class="game-status">
                    <span id="current-player">黑子先行</span>
                </div>
            </div>
            
            <!-- 右側玩家資訊 -->
            <div class="player-panel right-panel">
                <div class="player-info">
                    <div class="player-avatar white">⚪</div>
                    <div class="player-details">
                        <h3 id="white-player-name">白子</h3>
                        <div class="player-stats">
                            <div class="stat-item">
                                <span class="stat-label">俘虜:</span>
                                <span id="white-captures" class="stat-value">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">時間:</span>
                                <span id="white-time" class="stat-value">--:--</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- AI 思考指示器 (僅單機版顯示) -->
                <div id="ai-thinking" class="ai-thinking hidden">
                    <div class="thinking-animation">
                        <div class="thinking-dot"></div>
                        <div class="thinking-dot"></div>
                        <div class="thinking-dot"></div>
                    </div>
                    <p>AI 思考中...</p>
                </div>
                
                <div class="game-score">
                    <h4>目前局勢</h4>
                    <div class="score-display">
                        <div class="score-item">
                            <span class="score-label">黑子領域:</span>
                            <span id="black-territory" class="score-value">0</span>
                        </div>
                        <div class="score-item">
                            <span class="score-label">白子領域:</span>
                            <span id="white-territory" class="score-value">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- 遊戲結束對話框 -->
        <div id="game-end-modal" class="modal hidden">
            <div class="modal-content">
                <h2 id="game-result">遊戲結束</h2>
                <div class="final-score">
                    <div class="score-breakdown">
                        <h3>最終計分</h3>
                        <div class="score-details">
                            <div class="player-final-score">
                                <span class="player-name">黑子</span>
                                <div class="score-items">
                                    <span>領域: <span id="final-black-territory">0</span></span>
                                    <span>俘虜: <span id="final-black-captures">0</span></span>
                                    <span>總分: <span id="final-black-total">0</span></span>
                                </div>
                            </div>
                            <div class="player-final-score">
                                <span class="player-name">白子</span>
                                <div class="score-items">
                                    <span>領域: <span id="final-white-territory">0</span></span>
                                    <span>俘虜: <span id="final-white-captures">0</span></span>
                                    <span>貼目: <span id="final-komi">6.5</span></span>
                                    <span>總分: <span id="final-white-total">6.5</span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-buttons">
                    <button id="new-game-modal-btn" class="modal-btn primary">再來一局</button>
                    <button id="review-game-btn" class="modal-btn">複盤</button>
                    <button id="back-to-menu-modal-btn" class="modal-btn">返回選單</button>
                </div>
            </div>
        </div>
        
        <!-- 設定對話框 -->
        <div id="settings-modal" class="modal hidden">
            <div class="modal-content">
                <h2>遊戲設定</h2>
                <div class="settings-content">
                    <div class="setting-item">
                        <label for="show-coordinates">顯示座標</label>
                        <input type="checkbox" id="show-coordinates" checked>
                    </div>
                    <div class="setting-item">
                        <label for="show-last-move">標示最後一手</label>
                        <input type="checkbox" id="show-last-move" checked>
                    </div>
                    <div class="setting-item">
                        <label for="sound-effects">音效</label>
                        <input type="checkbox" id="sound-effects" checked>
                    </div>
                    <div class="setting-item">
                        <label for="ai-speed">AI 思考速度</label>
                        <select id="ai-speed">
                            <option value="fast">快速</option>
                            <option value="normal" selected>正常</option>
                            <option value="slow">緩慢</option>
                        </select>
                    </div>
                </div>
                <div class="modal-buttons">
                    <button id="save-settings-btn" class="modal-btn primary">儲存</button>
                    <button id="cancel-settings-btn" class="modal-btn">取消</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript 檔案 -->
    <script src="js/go-board.js"></script>
    <script src="js/go-game.js"></script>
    <script src="js/go-ai.js"></script>
    <script src="js/game-controller.js"></script>
</body>
</html>
