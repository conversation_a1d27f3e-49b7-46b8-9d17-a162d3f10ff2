<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>圍棋計分測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5dc;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-board {
            margin: 20px 0;
            text-align: center;
        }
        canvas {
            border: 2px solid #8B4513;
            border-radius: 5px;
        }
        .test-info {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .test-buttons {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #8B4513;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #A0522D;
        }
        .score-display {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .score-section {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            border: 2px solid #ddd;
        }
        .score-section h3 {
            margin-top: 0;
            color: #333;
        }
        .score-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px;
            background: white;
            border-radius: 3px;
        }
        .score-total {
            font-weight: bold;
            font-size: 1.1em;
            border-top: 2px solid #333;
            margin-top: 10px;
            padding-top: 10px;
        }
        .winner {
            text-align: center;
            font-size: 1.5em;
            font-weight: bold;
            color: #d32f2f;
            margin: 20px 0;
            padding: 15px;
            background: #ffebee;
            border-radius: 5px;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>圍棋計分系統測試</h1>
        
        <div class="test-info">
            <h3>測試說明：</h3>
            <p>1. 點擊棋盤下棋，建立一些領域</p>
            <p>2. 點擊「計算分數」查看目前的領域計算</p>
            <p>3. 點擊「顯示領域」可視化領域歸屬</p>
            <p>4. 點擊「設置測試局面」建立預設的計分測試</p>
        </div>
        
        <div class="test-board">
            <canvas id="test-board" width="500" height="500"></canvas>
        </div>
        
        <div class="status">
            <div>當前玩家: <span id="current-player">黑子</span></div>
            <div>手數: <span id="move-count">0</span></div>
            <div>最後訊息: <span id="last-message">開始遊戲</span></div>
        </div>
        
        <div class="test-buttons">
            <button onclick="resetTest()">重置測試</button>
            <button onclick="setupScoringTest()">設置測試局面</button>
            <button onclick="calculateScore()">計算分數</button>
            <button onclick="toggleTerritoryDisplay()">顯示/隱藏領域</button>
            <button onclick="simulateGameEnd()">模擬遊戲結束</button>
        </div>
        
        <div id="score-display" class="score-display" style="display: none;">
            <div class="score-section">
                <h3>⚫ 黑子</h3>
                <div class="score-item">
                    <span>領域:</span>
                    <span id="black-territory">0</span>
                </div>
                <div class="score-item">
                    <span>俘虜:</span>
                    <span id="black-captures">0</span>
                </div>
                <div class="score-item score-total">
                    <span>總分:</span>
                    <span id="black-total">0</span>
                </div>
            </div>
            
            <div class="score-section">
                <h3>⚪ 白子</h3>
                <div class="score-item">
                    <span>領域:</span>
                    <span id="white-territory">0</span>
                </div>
                <div class="score-item">
                    <span>俘虜:</span>
                    <span id="white-captures">0</span>
                </div>
                <div class="score-item">
                    <span>貼目:</span>
                    <span id="komi">6.5</span>
                </div>
                <div class="score-item score-total">
                    <span>總分:</span>
                    <span id="white-total">6.5</span>
                </div>
            </div>
        </div>
        
        <div id="winner-display" class="winner" style="display: none;"></div>
        
        <div class="test-info">
            <h3>計分規則：</h3>
            <p><strong>領域：</strong>被單一顏色棋子完全包圍的空白區域</p>
            <p><strong>俘虜：</strong>被吃掉的對方棋子數量</p>
            <p><strong>貼目：</strong>白子獲得 6.5 分補償（因為黑子先行優勢）</p>
            <p><strong>總分：</strong>領域 + 俘虜 + 貼目（僅白子）</p>
        </div>
    </div>

    <!-- 載入圍棋遊戲模組 -->
    <script src="js/go-game.js"></script>
    <script src="js/go-board.js"></script>
    
    <script>
        // 測試用的遊戲實例
        let testGame = new GoGame(13); // 使用 13x13 棋盤方便測試
        let testBoard = new GoBoard(document.getElementById('test-board'), 13);
        let showingTerritory = false;
        
        // 設置點擊事件
        testBoard.canvas.addEventListener('click', function(event) {
            const position = testBoard.getGridPosition(event.clientX, event.clientY);
            if (!position) return;
            
            const [row, col] = position;
            const result = testGame.makeMove(row, col);
            
            if (result.success) {
                // 同步棋盤狀態
                testBoard.setBoardState(testGame.board);
                testBoard.lastMove = [row, col];
                testBoard.drawBoard();
                
                // 更新狀態顯示
                updateStatus(result);
                
                if (result.captured > 0) {
                    document.getElementById('last-message').textContent = 
                        `吃掉了 ${result.captured} 個棋子！`;
                } else {
                    document.getElementById('last-message').textContent = 
                        `在 ${result.notation} 下了一子`;
                }
            } else {
                document.getElementById('last-message').textContent = result.error;
            }
        });
        
        function updateStatus(result) {
            document.getElementById('current-player').textContent = 
                testGame.currentPlayer === 1 ? '黑子' : '白子';
            document.getElementById('move-count').textContent = testGame.moveCount;
        }
        
        function resetTest() {
            testGame.reset();
            testBoard.clear();
            showingTerritory = false;
            updateStatus();
            document.getElementById('last-message').textContent = '遊戲重置';
            document.getElementById('score-display').style.display = 'none';
            document.getElementById('winner-display').style.display = 'none';
        }
        
        function setupScoringTest() {
            // 重置遊戲
            resetTest();
            
            // 設置一個有領域的測試局面
            // 黑子在左上角建立領域
            testGame.board[2][2] = 1;
            testGame.board[2][3] = 1;
            testGame.board[3][2] = 1;
            testGame.board[4][3] = 1;
            testGame.board[3][4] = 1;
            
            // 白子在右下角建立領域
            testGame.board[8][8] = 2;
            testGame.board[8][9] = 2;
            testGame.board[9][8] = 2;
            testGame.board[10][9] = 2;
            testGame.board[9][10] = 2;
            
            // 一些俘虜
            testGame.capturedStones.black = 3;
            testGame.capturedStones.white = 1;
            
            // 同步棋盤顯示
            testBoard.setBoardState(testGame.board);
            testBoard.drawBoard();
            
            updateStatus();
            document.getElementById('last-message').textContent = 
                '測試局面已設置！點擊「計算分數」查看結果';
        }
        
        function calculateScore() {
            try {
                const score = testGame.calculateFinalScore();
                
                // 更新分數顯示
                document.getElementById('black-territory').textContent = score.blackTerritory;
                document.getElementById('white-territory').textContent = score.whiteTerritory;
                document.getElementById('black-captures').textContent = score.blackCaptures;
                document.getElementById('white-captures').textContent = score.whiteCaptures;
                document.getElementById('komi').textContent = score.komi;
                document.getElementById('black-total').textContent = score.blackTotal;
                document.getElementById('white-total').textContent = score.whiteTotal;
                
                // 顯示分數區域
                document.getElementById('score-display').style.display = 'grid';
                
                // 顯示勝負
                const winner = score.blackTotal > score.whiteTotal ? '黑子' : '白子';
                const margin = Math.abs(score.blackTotal - score.whiteTotal);
                document.getElementById('winner-display').textContent = 
                    `${winner}獲勝！領先 ${margin} 分`;
                document.getElementById('winner-display').style.display = 'block';
                
                document.getElementById('last-message').textContent = '分數計算完成';
                
                console.log('詳細分數:', score);
            } catch (error) {
                console.error('計算分數時出錯:', error);
                document.getElementById('last-message').textContent = '計算分數時出錯: ' + error.message;
            }
        }
        
        function toggleTerritoryDisplay() {
            try {
                if (!showingTerritory) {
                    const score = testGame.calculateFinalScore();
                    testBoard.showTerritoryMarks(score.territoryMap);
                    showingTerritory = true;
                    document.getElementById('last-message').textContent = '顯示領域標記';
                } else {
                    testBoard.clearTerritoryMarks();
                    showingTerritory = false;
                    document.getElementById('last-message').textContent = '隱藏領域標記';
                }
            } catch (error) {
                console.error('顯示領域時出錯:', error);
                document.getElementById('last-message').textContent = '顯示領域時出錯: ' + error.message;
            }
        }
        
        function simulateGameEnd() {
            testGame.gameEnded = true;
            testGame.passCount = 2;
            calculateScore();
            document.getElementById('last-message').textContent = '模擬遊戲結束';
        }
        
        // 初始化
        updateStatus();
    </script>
</body>
</html>
