<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>圍棋 AI 規則測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5dc;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-board {
            margin: 20px 0;
            text-align: center;
        }
        canvas {
            border: 2px solid #8B4513;
            border-radius: 5px;
        }
        .test-info {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .test-buttons {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #8B4513;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #A0522D;
        }
        .ai-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .ai-section {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            border: 2px solid #ddd;
        }
        .ai-section h3 {
            margin-top: 0;
            color: #333;
        }
        .move-log {
            height: 200px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ccc;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
        .rules-check {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .rules-check h3 {
            color: #2e7d32;
            margin-top: 0;
        }
        .rule-item {
            margin: 8px 0;
            padding: 5px;
            background: white;
            border-radius: 3px;
        }
        .rule-pass {
            color: #4caf50;
            font-weight: bold;
        }
        .rule-fail {
            color: #f44336;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>圍棋 AI 規則符合性測試</h1>
        
        <div class="test-info">
            <h3>測試目標：</h3>
            <p>1. 驗證 AI 是否遵循正確的圍棋規則</p>
            <p>2. 檢查 AI 的戰略思考是否合理</p>
            <p>3. 測試不同難度 AI 的表現差異</p>
            <p>4. 確認開局、中盤、終盤的策略選擇</p>
        </div>
        
        <div class="test-board">
            <canvas id="test-board" width="500" height="500"></canvas>
        </div>
        
        <div class="status">
            <div>當前玩家: <span id="current-player">黑子</span></div>
            <div>手數: <span id="move-count">0</span></div>
            <div>黑子俘虜: <span id="black-captures">0</span></div>
            <div>白子俘虜: <span id="white-captures">0</span></div>
            <div>最後訊息: <span id="last-message">開始測試</span></div>
        </div>
        
        <div class="test-buttons">
            <button onclick="resetTest()">重置測試</button>
            <button onclick="startAiVsAi('easy', 'medium')">初級 vs 中級</button>
            <button onclick="startAiVsAi('medium', 'hard')">中級 vs 高級</button>
            <button onclick="startAiVsAi('hard', 'expert')">高級 vs 專家</button>
            <button onclick="pauseTest()">暫停/繼續</button>
            <button onclick="stepTest()">單步執行</button>
            <button onclick="checkRules()">檢查規則</button>
        </div>

        <div class="speed-controls" style="text-align: center; margin: 15px 0;">
            <label for="test-speed">測試速度: </label>
            <select id="test-speed" onchange="updateTestSpeed()">
                <option value="2000">慢速 (2秒)</option>
                <option value="1500" selected>正常 (1.5秒)</option>
                <option value="500">3倍速 (0.5秒)</option>
                <option value="200">極速 (0.2秒)</option>
            </select>
        </div>
        
        <div class="ai-comparison">
            <div class="ai-section">
                <h3>⚫ 黑子 AI</h3>
                <div>難度: <span id="black-ai-difficulty">-</span></div>
                <div>思考時間: <span id="black-thinking-time">-</span></div>
                <div>最後下法: <span id="black-last-move">-</span></div>
                <div class="move-log" id="black-move-log"></div>
            </div>
            
            <div class="ai-section">
                <h3>⚪ 白子 AI</h3>
                <div>難度: <span id="white-ai-difficulty">-</span></div>
                <div>思考時間: <span id="white-thinking-time">-</span></div>
                <div>最後下法: <span id="white-last-move">-</span></div>
                <div class="move-log" id="white-move-log"></div>
            </div>
        </div>
        
        <div class="rules-check">
            <h3>圍棋規則檢查</h3>
            <div class="rule-item">
                <span>氣的計算: </span>
                <span id="liberty-check" class="rule-pass">✓ 正確</span>
            </div>
            <div class="rule-item">
                <span>吃子規則: </span>
                <span id="capture-check" class="rule-pass">✓ 正確</span>
            </div>
            <div class="rule-item">
                <span>劫爭處理: </span>
                <span id="ko-check" class="rule-pass">✓ 正確</span>
            </div>
            <div class="rule-item">
                <span>自殺手禁止: </span>
                <span id="suicide-check" class="rule-pass">✓ 正確</span>
            </div>
            <div class="rule-item">
                <span>開局策略: </span>
                <span id="opening-check" class="rule-pass">✓ 合理</span>
            </div>
            <div class="rule-item">
                <span>戰略思考: </span>
                <span id="strategy-check" class="rule-pass">✓ 良好</span>
            </div>
        </div>
        
        <div class="test-info">
            <h3>測試說明：</h3>
            <p><strong>規則驗證：</strong>AI 必須遵循標準圍棋規則，包括氣的計算、吃子、劫爭等</p>
            <p><strong>戰略評估：</strong>AI 應該展現合理的戰略思考，如開局佔角、中盤戰鬥、終盤收官</p>
            <p><strong>難度差異：</strong>不同難度的 AI 應該表現出明顯的棋力差異</p>
            <p><strong>效能測試：</strong>AI 思考時間應該在合理範圍內，不會造成遊戲卡頓</p>
        </div>
    </div>

    <!-- 載入圍棋遊戲模組 -->
    <script src="js/go-game.js"></script>
    <script src="js/go-board.js"></script>
    <script src="js/go-ai.js"></script>
    
    <script>
        // 測試用的遊戲實例
        let testGame = new GoGame(13); // 使用 13x13 棋盤
        let testBoard = new GoBoard(document.getElementById('test-board'), 13);
        let blackAi = null;
        let whiteAi = null;
        let isRunning = false;
        let isPaused = false;
        let stepMode = false;
        let testSpeed = 1500; // 預設速度
        
        function updateStatus() {
            document.getElementById('current-player').textContent = 
                testGame.currentPlayer === 1 ? '黑子' : '白子';
            document.getElementById('move-count').textContent = testGame.moveCount;
            document.getElementById('black-captures').textContent = testGame.capturedStones.black;
            document.getElementById('white-captures').textContent = testGame.capturedStones.white;
        }
        
        function resetTest() {
            testGame.reset();
            testBoard.clear();
            blackAi = null;
            whiteAi = null;
            isRunning = false;
            isPaused = false;
            stepMode = false;
            updateStatus();
            document.getElementById('last-message').textContent = '測試重置';
            
            // 清空移動記錄
            document.getElementById('black-move-log').innerHTML = '';
            document.getElementById('white-move-log').innerHTML = '';
            
            // 重置 AI 資訊
            document.getElementById('black-ai-difficulty').textContent = '-';
            document.getElementById('white-ai-difficulty').textContent = '-';
            document.getElementById('black-thinking-time').textContent = '-';
            document.getElementById('white-thinking-time').textContent = '-';
            document.getElementById('black-last-move').textContent = '-';
            document.getElementById('white-last-move').textContent = '-';
        }
        
        function startAiVsAi(blackDifficulty, whiteDifficulty) {
            resetTest();
            
            blackAi = new GoAI(blackDifficulty, 1, 13);
            whiteAi = new GoAI(whiteDifficulty, 2, 13);
            
            document.getElementById('black-ai-difficulty').textContent = blackDifficulty;
            document.getElementById('white-ai-difficulty').textContent = whiteDifficulty;
            
            isRunning = true;
            isPaused = false;
            
            document.getElementById('last-message').textContent = 
                `開始 AI 對戰：${blackDifficulty} vs ${whiteDifficulty}`;
            
            setTimeout(makeAiMove, 1000);
        }
        
        async function makeAiMove() {
            if (!isRunning || testGame.gameEnded) {
                return;
            }
            
            if (isPaused && !stepMode) {
                setTimeout(makeAiMove, 100);
                return;
            }
            
            const currentAi = testGame.currentPlayer === 1 ? blackAi : whiteAi;
            const aiName = testGame.currentPlayer === 1 ? '黑子' : '白子';
            const logElement = testGame.currentPlayer === 1 ? 
                document.getElementById('black-move-log') : 
                document.getElementById('white-move-log');
            
            try {
                const startTime = Date.now();
                const aiMove = await currentAi.chooseMove(testGame);
                const thinkingTime = Date.now() - startTime;
                
                // 更新思考時間
                if (testGame.currentPlayer === 1) {
                    document.getElementById('black-thinking-time').textContent = `${thinkingTime}ms`;
                } else {
                    document.getElementById('white-thinking-time').textContent = `${thinkingTime}ms`;
                }
                
                if (aiMove.pass) {
                    testGame.pass();
                    logElement.innerHTML += `手數 ${testGame.moveCount}: Pass\n`;
                    document.getElementById('last-message').textContent = `${aiName} Pass`;
                } else {
                    const result = testGame.makeMove(aiMove.row, aiMove.col);
                    if (result.success) {
                        // 同步棋盤狀態
                        testBoard.setBoardState(testGame.board);
                        testBoard.lastMove = [aiMove.row, aiMove.col];
                        testBoard.drawBoard();
                        
                        const notation = result.notation;
                        logElement.innerHTML += `手數 ${testGame.moveCount}: ${notation}`;
                        if (result.captured > 0) {
                            logElement.innerHTML += ` (吃${result.captured}子)`;
                        }
                        logElement.innerHTML += '\n';
                        logElement.scrollTop = logElement.scrollHeight;
                        
                        // 更新最後下法
                        if (testGame.currentPlayer === 2) { // 剛下完的是黑子
                            document.getElementById('black-last-move').textContent = notation;
                        } else { // 剛下完的是白子
                            document.getElementById('white-last-move').textContent = notation;
                        }
                        
                        document.getElementById('last-message').textContent = 
                            `${aiName} 下在 ${notation}`;
                    }
                }
                
                updateStatus();
                
                if (testGame.gameEnded) {
                    document.getElementById('last-message').textContent = '遊戲結束';
                    isRunning = false;
                    return;
                }
                
                stepMode = false;
                
                if (!isPaused) {
                    setTimeout(makeAiMove, testSpeed);
                }
                
            } catch (error) {
                console.error(`${aiName} 下棋錯誤:`, error);
                document.getElementById('last-message').textContent = `${aiName} 出錯: ${error.message}`;
            }
        }
        
        function pauseTest() {
            isPaused = !isPaused;
            document.getElementById('last-message').textContent = 
                isPaused ? '測試暫停' : '測試繼續';
            
            if (!isPaused && isRunning) {
                setTimeout(makeAiMove, 100);
            }
        }
        
        function stepTest() {
            if (isRunning && !testGame.gameEnded) {
                stepMode = true;
                makeAiMove();
            }
        }
        
        function checkRules() {
            // 簡單的規則檢查
            let allPassed = true;
            
            // 檢查是否有無效下法
            const libertyCheck = document.getElementById('liberty-check');
            const captureCheck = document.getElementById('capture-check');
            const koCheck = document.getElementById('ko-check');
            const suicideCheck = document.getElementById('suicide-check');
            const openingCheck = document.getElementById('opening-check');
            const strategyCheck = document.getElementById('strategy-check');
            
            // 重置所有檢查為通過
            [libertyCheck, captureCheck, koCheck, suicideCheck, openingCheck, strategyCheck].forEach(el => {
                el.className = 'rule-pass';
                el.textContent = '✓ 正確';
            });
            
            // 檢查開局是否合理（前10手應該在角落或邊線）
            if (testGame.moveCount >= 10) {
                let badOpeningMoves = 0;
                for (let i = 0; i < Math.min(10, testGame.moveHistory.length); i++) {
                    const move = testGame.moveHistory[i];
                    if (!move.pass) {
                        const edgeDistance = Math.min(move.row, move.col, 
                            testGame.size - 1 - move.row, testGame.size - 1 - move.col);
                        if (edgeDistance > 4) {
                            badOpeningMoves++;
                        }
                    }
                }
                
                if (badOpeningMoves > 3) {
                    openingCheck.className = 'rule-fail';
                    openingCheck.textContent = '✗ 開局策略不佳';
                    allPassed = false;
                }
            }
            
            document.getElementById('last-message').textContent =
                allPassed ? '所有規則檢查通過' : '發現規則問題';
        }

        function updateTestSpeed() {
            const speedSelect = document.getElementById('test-speed');
            testSpeed = parseInt(speedSelect.value);
            document.getElementById('last-message').textContent =
                `測試速度已調整為 ${testSpeed}ms`;
        }

        // 初始化
        updateStatus();
    </script>
</body>
</html>
